interface HeaderProps {
  isSidebarCollapsed?: boolean;
}

const Header = ({ isSidebarCollapsed = false }: HeaderProps) => {
  return (
    <header className={`
      sticky top-0 z-50 transition-all duration-300
      ${isSidebarCollapsed
        ? 'bg-transparent border-b border-white/5'
        : 'bg-slate-900/80 backdrop-blur-sm border-b border-slate-700/50'
      }
    `}>
      <div className="flex items-center justify-center p-4">
        {/* Centered Logo */}
        <img src="/mixfade_logo.png" alt="MixFade Logo" className="h-10" />
      </div>
    </header>
  );
};

export default Header;

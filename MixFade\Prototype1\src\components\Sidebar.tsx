import React from 'react';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { activities } from './ActivityBar';

// Import sidebar panels
import { FilesPanel } from './sidebar/FilesPanel';
import { AnalysisPanel } from './sidebar/AnalysisPanel';
import { EffectsPanel } from './sidebar/EffectsPanel';
import { LibraryPanel } from './sidebar/LibraryPanel';
import { SettingsPanel } from './sidebar/SettingsPanel';
import { HelpPanel } from './sidebar/HelpPanel';

// Recent files interface
interface RecentFile {
  id: string;
  name: string;
  size: string;
  lastModified: string;
  lastUsedSide: 'A' | 'B';
  file?: File; // Keep File object in memory during session
  filePath?: string; // For future file system access
}

interface SidebarProps {
  activeActivity: string;
  isCollapsed: boolean;
  onToggle: () => void;
  className?: string;
  recentFiles?: RecentFile[];
  onLoadFileFromRecent?: (recentFile: RecentFile) => void;
  onLoadToA?: (file: File | null) => void;
  onLoadToB?: (file: File | null) => void;
}

export function Sidebar({
  activeActivity,
  isCollapsed,
  onToggle,
  className = '',
  recentFiles = [],
  onLoadFileFromRecent,
  onLoadToA,
  onLoadToB
}: SidebarProps) {
  // Get current activity info
  const currentActivity = activities.find(activity => activity.id === activeActivity);

  // Render appropriate panel content
  const renderContent = () => {
    switch (activeActivity) {
      case 'files':
        return (
          <FilesPanel
            recentFiles={recentFiles}
            onLoadFileFromRecent={onLoadFileFromRecent}
            onLoadToA={onLoadToA}
            onLoadToB={onLoadToB}
          />
        );
      case 'analysis':
        return <AnalysisPanel />;
      case 'effects':
        return <EffectsPanel />;
      case 'library':
        return <LibraryPanel />;
      case 'settings':
        return <SettingsPanel />;
      case 'help':
        return <HelpPanel />;
      default:
        return (
          <div className="p-4 text-center text-slate-400">
            <p>Select an activity to get started</p>
          </div>
        );
    }
  };

  if (isCollapsed) {
    return (
      <div className={`w-0 relative transition-all duration-300 ${className}`}>
        {/* Toggle button positioned on the collapsed sidebar */}
        <button
          onClick={onToggle}
          className="
            absolute left-0 top-1/2 transform -translate-y-1/2 z-10
            w-6 h-12 bg-slate-800 border border-slate-700 rounded-r-md
            flex items-center justify-center
            text-slate-400 hover:text-slate-200 hover:bg-slate-700
            transition-all duration-200
          "
          title="Show Sidebar (Ctrl+B)"
          aria-label="Show Sidebar"
        >
          <ChevronRight size={14} />
        </button>
      </div>
    );
  }

  return (
    <div className={`w-64 bg-slate-900 border-r border-slate-700 flex flex-col transition-all duration-300 ${className}`}>
      {/* Header */}
      <div className="h-12 flex items-center justify-between px-4 border-b border-slate-700 bg-slate-800">
        <div className="flex items-center space-x-2">
          {currentActivity?.icon && (
            <currentActivity.icon size={16} className="text-slate-400" />
          )}
          <h2 className="text-sm font-medium text-slate-200 capitalize">
            {currentActivity?.label || 'Unknown'}
          </h2>
        </div>
        
        <button
          onClick={onToggle}
          className="p-1 text-slate-400 hover:text-slate-200 hover:bg-slate-700 rounded transition-colors"
          title="Toggle Sidebar (Ctrl+B)"
          aria-label="Toggle Sidebar"
        >
          <ChevronLeft size={16} />
        </button>
      </div>
      
      {/* Content */}
      <div className="flex-1 overflow-y-auto">
        {renderContent()}
      </div>
      
      {/* Footer (optional) */}
      <div className="border-t border-slate-700 p-2">
        <div className="text-xs text-slate-500 text-center">
          {currentActivity?.shortcut && (
            <span>Press {currentActivity.shortcut}</span>
          )}
        </div>
      </div>
    </div>
  );
}



import React from 'react';
import { Folder, Slide<PERSON>, Zap, Music, Settings, HelpCircle } from 'lucide-react';

export interface ActivityItem {
  id: string;
  icon: React.ComponentType<{ size?: number; className?: string }>;
  label: string;
  badge?: number;
  shortcut?: string;
}

interface ActivityBarProps {
  activeId: string;
  onActivityChange: (id: string) => void;
  className?: string;
}

// Define activities for MixFade
export const activities: ActivityItem[] = [
  { 
    id: 'files', 
    icon: Folder, 
    label: 'Files', 
    shortcut: 'Ctrl+Shift+E'
  },
  { 
    id: 'mixer', 
    icon: Sliders, 
    label: 'Mixer', 
    shortcut: 'Ctrl+Shift+M'
  },
  { 
    id: 'effects', 
    icon: Zap, 
    label: 'Effects', 
    shortcut: 'Ctrl+Shift+X'
  },
  { 
    id: 'library', 
    icon: Music, 
    label: 'Library', 
    shortcut: 'Ctrl+Shift+L'
  },
  { 
    id: 'settings', 
    icon: Settings, 
    label: 'Settings', 
    shortcut: 'Ctrl+,'
  },
  { 
    id: 'help', 
    icon: HelpCircle, 
    label: 'Help', 
    shortcut: 'F1'
  }
];

export function ActivityBar({ activeId, onActivityChange, className = '' }: ActivityBarProps) {
  return (
    <div className={`w-12 bg-slate-800 border-r border-slate-700 flex flex-col ${className}`}>
      {/* Main Activities */}
      <div className="flex-1">
        {activities.slice(0, 4).map(({ id, icon: Icon, label, badge, shortcut }) => (
          <button
            key={id}
            onClick={() => onActivityChange(id)}
            className={`
              w-12 h-12 flex items-center justify-center relative group
              hover:bg-slate-700 transition-all duration-200
              ${activeId === id 
                ? 'bg-slate-600 border-r-2 border-emerald-500 text-emerald-400' 
                : 'text-slate-400 hover:text-slate-200'
              }
            `}
            title={`${label} (${shortcut})`}
            aria-label={label}
          >
            <Icon size={20} className="transition-colors duration-200" />
            
            {/* Badge for notifications */}
            {badge && badge > 0 && (
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center font-medium">
                {badge > 99 ? '99+' : badge}
              </span>
            )}
            
            {/* Active indicator */}
            {activeId === id && (
              <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-6 bg-emerald-500 rounded-r-sm" />
            )}
          </button>
        ))}
      </div>
      
      {/* Bottom Activities (Settings, Help) */}
      <div className="border-t border-slate-700">
        {activities.slice(4).map(({ id, icon: Icon, label, badge, shortcut }) => (
          <button
            key={id}
            onClick={() => onActivityChange(id)}
            className={`
              w-12 h-12 flex items-center justify-center relative group
              hover:bg-slate-700 transition-all duration-200
              ${activeId === id 
                ? 'bg-slate-600 border-r-2 border-emerald-500 text-emerald-400' 
                : 'text-slate-400 hover:text-slate-200'
              }
            `}
            title={`${label} (${shortcut})`}
            aria-label={label}
          >
            <Icon size={20} className="transition-colors duration-200" />
            
            {/* Badge for notifications */}
            {badge && badge > 0 && (
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center font-medium">
                {badge > 99 ? '99+' : badge}
              </span>
            )}
            
            {/* Active indicator */}
            {activeId === id && (
              <div className="absolute left-0 top-1/2 transform -translate-y-1/2 w-1 h-6 bg-emerald-500 rounded-r-sm" />
            )}
          </button>
        ))}
      </div>
    </div>
  );
}

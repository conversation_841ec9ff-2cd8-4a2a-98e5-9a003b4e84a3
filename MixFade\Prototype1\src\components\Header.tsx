interface HeaderProps {
  isSidebarCollapsed?: boolean;
}

const Header = ({ isSidebarCollapsed = false }: HeaderProps) => {
  console.log('Header render - isSidebarCollapsed:', isSidebarCollapsed);

  const headerClasses = isSidebarCollapsed
    ? "sticky top-0 z-50 transition-all duration-300"
    : "sticky top-0 z-50 transition-all duration-300 !bg-slate-900 border-b border-slate-600/50";

  return (
    <header
      className={headerClasses}
      style={{ minHeight: '60px' }}>
      <div className="flex items-center justify-center p-4">
        {/* Centered Logo */}
        <img src="/mixfade_logo.png" alt="MixFade Logo" className="h-10" />
      </div>
    </header>
  );
};

export default Header;

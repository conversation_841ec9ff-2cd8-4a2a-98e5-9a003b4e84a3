# Electron UI Best Practices - Learning from Successful Apps

## Overview
This guide outlines UI and navigation best practices learned from successful Electron applications like **VS Code**, **Discord**, **Figma**, **GitKraken**, and **Slack**.

## 1. Navigation Architecture Patterns

### ✅ The "Activity Bar + Sidebar" Pattern (VS Code Style)
**What it is:**
- **Activity Bar**: Narrow vertical bar on the left with primary navigation icons
- **Primary Sidebar**: Expandable panel showing context for selected activity
- **Secondary Sidebar**: Optional right-side panel for additional tools
- **Main Content Area**: Central workspace for primary tasks

**Why it works:**
- **Scalable**: Easy to add new features without cluttering
- **Contextual**: Sidebar content changes based on activity
- **Efficient**: Quick switching between different modes
- **Familiar**: Users understand the pattern from VS Code

### ✅ The "Server/Channel" Pattern (Discord Style)
**What it is:**
- **Server List**: Vertical list of "workspaces" or "projects"
- **Channel List**: Secondary navigation within selected server
- **Main Content**: Primary interaction area
- **Member List**: Context-sensitive right panel

**Why it works:**
- **Hierarchical**: Clear information architecture
- **Contextual**: Content changes based on selection
- **Visual**: Icons and colors for quick recognition

## 2. Sidebar Design Best Practices

### Layout Principles
1. **Consistent Width**: 240-280px is optimal for most content
2. **Collapsible**: Allow users to hide/show for more space
3. **Resizable**: Let users adjust width to their preference
4. **Sticky Headers**: Keep section headers visible during scroll

### Content Organization
```
📁 Primary Actions (top)
├── New Project
├── Open Recent
└── Import

📊 Current Context (middle)
├── Project Files
├── Recent Items
└── Bookmarks

⚙️ Settings/Tools (bottom)
├── Preferences
├── Help
└── Account
```

### Visual Hierarchy
- **Icons**: 16-20px for list items, 24px for primary actions
- **Typography**: 14px for primary text, 12px for secondary
- **Spacing**: 8px between items, 16px between sections
- **Colors**: Subtle backgrounds for hover states

## 3. Activity Bar Design

### Icon Guidelines
- **Size**: 24x24px icons in 48x48px touch targets
- **Style**: Consistent stroke width (1.5-2px)
- **States**: Normal, hover, active, disabled
- **Badges**: Small notification indicators (max 2 digits)

### Interaction Patterns
```typescript
// Example activity items for MixFade
const activities = [
  { id: 'files', icon: 'Folder', label: 'Files' },
  { id: 'mixer', icon: 'Sliders', label: 'Mixer' },
  { id: 'effects', icon: 'Zap', label: 'Effects' },
  { id: 'library', icon: 'Music', label: 'Library' },
  { id: 'settings', icon: 'Settings', label: 'Settings' }
];
```

## 4. Content Area Layout

### Grid System
- **12-column grid** for responsive layouts
- **8px base unit** for consistent spacing
- **Flexible panels** that can be resized/rearranged

### Panel Management
- **Dockable panels**: Users can move panels around
- **Tabbed interfaces**: Group related content
- **Split views**: Side-by-side comparisons
- **Floating windows**: Multi-monitor support

## 5. Modern UI Patterns

### Command Palette (VS Code Style)
```typescript
// Keyboard shortcut: Ctrl/Cmd + Shift + P
const commands = [
  'File: Open Audio File',
  'Mixer: Add Track',
  'Effects: Apply Reverb',
  'View: Toggle Sidebar',
  'Help: Show Shortcuts'
];
```

### Quick Open (VS Code Style)
```typescript
// Keyboard shortcut: Ctrl/Cmd + P
// Fuzzy search through files, recent items, etc.
```

### Context Menus
- **Right-click everywhere**: Contextual actions
- **Keyboard accessible**: Arrow keys + Enter
- **Grouped actions**: Separators between action types

## 6. Theme and Visual Design

### Color System
```css
/* Dark Theme (Primary) */
--bg-primary: #1e1e1e;
--bg-secondary: #252526;
--bg-tertiary: #2d2d30;
--text-primary: #cccccc;
--text-secondary: #969696;
--accent: #007acc;
--border: #3e3e42;

/* Light Theme (Secondary) */
--bg-primary: #ffffff;
--bg-secondary: #f3f3f3;
--bg-tertiary: #e8e8e8;
--text-primary: #333333;
--text-secondary: #666666;
--accent: #0078d4;
--border: #d1d1d1;
```

### Typography Scale
```css
--font-size-xs: 11px;   /* Captions, labels */
--font-size-sm: 12px;   /* Secondary text */
--font-size-md: 14px;   /* Body text */
--font-size-lg: 16px;   /* Headings */
--font-size-xl: 20px;   /* Page titles */
```

## 7. Responsive Behavior

### Breakpoints
- **Large**: >1200px - Full layout with all panels
- **Medium**: 768-1200px - Collapsible sidebars
- **Small**: <768px - Mobile-first, overlay panels

### Adaptive UI
- **Auto-hide**: Panels collapse when space is limited
- **Priority**: Most important content stays visible
- **Touch-friendly**: Larger targets on touch devices

## 8. Accessibility Best Practices

### Keyboard Navigation
- **Tab order**: Logical flow through interface
- **Focus indicators**: Clear visual feedback
- **Shortcuts**: Consistent with platform conventions
- **Screen readers**: Proper ARIA labels

### Visual Accessibility
- **Contrast ratios**: WCAG AA compliance (4.5:1)
- **Color independence**: Don't rely solely on color
- **Text scaling**: Support up to 200% zoom
- **Motion**: Respect reduced motion preferences

## 9. Performance Considerations

### Virtual Scrolling
```typescript
// For large lists (>1000 items)
import { FixedSizeList as List } from 'react-window';
```

### Lazy Loading
- **Images**: Load as they enter viewport
- **Panels**: Initialize when first opened
- **Data**: Fetch on demand

### Memory Management
- **Component cleanup**: Remove event listeners
- **Image optimization**: Proper sizing and formats
- **State management**: Avoid memory leaks

## 10. Implementation Recommendations for MixFade

### Suggested Layout
```
┌─────┬──────────────────────────────────────┬─────┐
│  A  │                                      │  S  │
│  C  │                                      │  I  │
│  T  │           MAIN CONTENT               │  D  │
│  I  │         (Waveform/Mixer)             │  E  │
│  V  │                                      │  B  │
│  I  │                                      │  A  │
│  T  │                                      │  R  │
│  Y  │                                      │     │
└─────┴──────────────────────────────────────┴─────┘
```

### Activity Bar Items
1. **Files** 📁 - File browser and recent projects
2. **Mixer** 🎚️ - Track controls and crossfader
3. **Effects** ⚡ - Audio effects and filters
4. **Library** 🎵 - Music library and playlists
5. **Settings** ⚙️ - Preferences and configuration

### Sidebar Content Examples
- **Files**: Project tree, recent files, import options
- **Mixer**: Track levels, EQ controls, crossfade settings
- **Effects**: Effect presets, real-time controls
- **Library**: Search, categories, favorites
- **Settings**: Audio devices, shortcuts, themes

## 11. Next Steps for Implementation

1. **Create Activity Bar Component** with icon switching
2. **Build Sidebar System** with contextual content
3. **Implement Command Palette** for power users
4. **Add Keyboard Shortcuts** for all major actions
5. **Design Context Menus** for right-click actions
6. **Create Theme System** with dark/light modes
7. **Add Panel Management** for customizable layout

This architecture will give MixFade a professional, familiar feel while maintaining the flexibility to grow and adapt to user needs.

## 12. Practical Implementation Examples

### Activity Bar Component
```typescript
// components/ActivityBar.tsx
import { Folder, Sliders, Zap, Music, Settings } from 'lucide-react';

interface ActivityItem {
  id: string;
  icon: React.ComponentType;
  label: string;
  badge?: number;
}

const activities: ActivityItem[] = [
  { id: 'files', icon: Folder, label: 'Files' },
  { id: 'mixer', icon: Sliders, label: 'Mixer' },
  { id: 'effects', icon: Zap, label: 'Effects' },
  { id: 'library', icon: Music, label: 'Library' },
  { id: 'settings', icon: Settings, label: 'Settings' }
];

export function ActivityBar({ activeId, onActivityChange }) {
  return (
    <div className="w-12 bg-slate-800 flex flex-col">
      {activities.map(({ id, icon: Icon, label, badge }) => (
        <button
          key={id}
          onClick={() => onActivityChange(id)}
          className={`
            h-12 flex items-center justify-center relative
            hover:bg-slate-700 transition-colors
            ${activeId === id ? 'bg-slate-600 border-r-2 border-emerald-500' : ''}
          `}
          title={label}
        >
          <Icon size={20} className="text-slate-300" />
          {badge && (
            <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">
              {badge}
            </span>
          )}
        </button>
      ))}
    </div>
  );
}
```

### Sidebar System
```typescript
// components/Sidebar.tsx
interface SidebarProps {
  activeActivity: string;
  isCollapsed: boolean;
  onToggle: () => void;
}

export function Sidebar({ activeActivity, isCollapsed, onToggle }) {
  const renderContent = () => {
    switch (activeActivity) {
      case 'files':
        return <FilesPanel />;
      case 'mixer':
        return <MixerPanel />;
      case 'effects':
        return <EffectsPanel />;
      case 'library':
        return <LibraryPanel />;
      case 'settings':
        return <SettingsPanel />;
      default:
        return null;
    }
  };

  if (isCollapsed) return null;

  return (
    <div className="w-64 bg-slate-900 border-r border-slate-700 flex flex-col">
      <div className="h-10 flex items-center justify-between px-4 border-b border-slate-700">
        <h2 className="text-sm font-medium text-slate-300 capitalize">
          {activeActivity}
        </h2>
        <button
          onClick={onToggle}
          className="text-slate-400 hover:text-slate-200"
        >
          <ChevronLeft size={16} />
        </button>
      </div>
      <div className="flex-1 overflow-y-auto">
        {renderContent()}
      </div>
    </div>
  );
}
```

### Command Palette
```typescript
// components/CommandPalette.tsx
import { useState, useEffect } from 'react';

const commands = [
  { id: 'file.open', label: 'File: Open Audio File', action: 'openFile' },
  { id: 'mixer.add', label: 'Mixer: Add Track', action: 'addTrack' },
  { id: 'effects.reverb', label: 'Effects: Apply Reverb', action: 'applyReverb' },
  { id: 'view.sidebar', label: 'View: Toggle Sidebar', action: 'toggleSidebar' },
  { id: 'help.shortcuts', label: 'Help: Show Shortcuts', action: 'showShortcuts' }
];

export function CommandPalette({ isOpen, onClose, onCommand }) {
  const [query, setQuery] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(0);

  const filteredCommands = commands.filter(cmd =>
    cmd.label.toLowerCase().includes(query.toLowerCase())
  );

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(i => Math.min(i + 1, filteredCommands.length - 1));
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(i => Math.max(i - 1, 0));
          break;
        case 'Enter':
          e.preventDefault();
          if (filteredCommands[selectedIndex]) {
            onCommand(filteredCommands[selectedIndex].action);
            onClose();
          }
          break;
        case 'Escape':
          onClose();
          break;
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, selectedIndex, filteredCommands, onCommand, onClose]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-start justify-center pt-20 z-50">
      <div className="bg-slate-800 rounded-lg shadow-xl w-96 max-h-96 overflow-hidden">
        <input
          type="text"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          placeholder="Type a command..."
          className="w-full px-4 py-3 bg-transparent text-white placeholder-slate-400 border-b border-slate-700 focus:outline-none"
          autoFocus
        />
        <div className="max-h-64 overflow-y-auto">
          {filteredCommands.map((cmd, index) => (
            <button
              key={cmd.id}
              onClick={() => {
                onCommand(cmd.action);
                onClose();
              }}
              className={`
                w-full px-4 py-2 text-left text-sm
                ${index === selectedIndex ? 'bg-slate-700' : 'hover:bg-slate-700'}
                text-slate-200
              `}
            >
              {cmd.label}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
```

### Keyboard Shortcuts Hook
```typescript
// hooks/useKeyboardShortcuts.ts
import { useEffect } from 'react';

export function useKeyboardShortcuts(handlers: Record<string, () => void>) {
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      const key = [
        e.ctrlKey && 'ctrl',
        e.metaKey && 'cmd',
        e.shiftKey && 'shift',
        e.altKey && 'alt',
        e.key.toLowerCase()
      ].filter(Boolean).join('+');

      if (handlers[key]) {
        e.preventDefault();
        handlers[key]();
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [handlers]);
}

// Usage in App.tsx
const shortcuts = {
  'ctrl+shift+p': () => setCommandPaletteOpen(true),
  'ctrl+p': () => setQuickOpenOpen(true),
  'ctrl+b': () => setSidebarCollapsed(!sidebarCollapsed),
  'ctrl+o': () => openFile(),
  'ctrl+s': () => saveProject(),
  'f11': () => toggleFullscreen()
};

useKeyboardShortcuts(shortcuts);
```

This implementation provides a solid foundation for a professional Electron app interface that users will find familiar and efficient.

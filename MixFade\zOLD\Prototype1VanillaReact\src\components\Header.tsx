import { Settings } from 'lucide-react';

const Header = () => {
  return (
    <header className="glass-panel sticky top-0 z-50 border-b border-white/10">
      <div className="flex items-center justify-between p-4">
        <div className="flex items-center space-x-4">
          {/* Logo */}
          <img src="/mixfade_logo.png" alt="MixFade Logo" className="h-10" />
        </div>
        
        {/* Controls */}
        <div className="flex items-center space-x-2">
          <button className="p-2 rounded-lg bg-white/5 hover:bg-gradient-to-r hover:from-emerald-500 hover:to-purple-500 transition-colors duration-200 group">
            <Settings 
              size={18} 
              className="text-white/60 group-hover:text-white group-hover:rotate-90 transition-all duration-300" 
            />
          </button>
        </div>
      </div>
    </header>
  );
};

export default Header;

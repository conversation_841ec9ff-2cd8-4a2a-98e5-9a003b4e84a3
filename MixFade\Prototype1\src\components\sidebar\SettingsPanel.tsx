import React from 'react';
import { BarChart3, Volume2, Monitor, Download } from 'lucide-react';

export function SettingsPanel() {
  return (
    <div className="p-4 space-y-6">
      {/* Analysis Settings */}
      <div className="space-y-2">
        <h3 className="text-xs font-semibold text-slate-400 uppercase tracking-wide">Analysis</h3>
        <div className="space-y-3">
          <div className="space-y-2">
            <label className="text-sm text-slate-300">FFT Size</label>
            <select className="w-full px-3 py-2 bg-slate-800 border border-slate-700 rounded-md text-sm text-slate-300 focus:outline-none focus:border-emerald-500">
              <option>1024</option>
              <option>2048</option>
              <option>4096</option>
              <option>8192</option>
            </select>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm text-slate-300">Update Rate</label>
              <span className="text-xs text-slate-500">30 FPS</span>
            </div>
            <input
              type="range"
              min="10"
              max="60"
              defaultValue="30"
              className="w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer slider"
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm text-slate-300">Frequency Range</label>
            <select className="w-full px-3 py-2 bg-slate-800 border border-slate-700 rounded-md text-sm text-slate-300 focus:outline-none focus:border-emerald-500">
              <option>20 Hz - 20 kHz (Full)</option>
              <option>50 Hz - 15 kHz (Music)</option>
              <option>100 Hz - 10 kHz (Voice)</option>
            </select>
          </div>
        </div>
      </div>

      {/* Meter Settings */}
      <div className="space-y-2">
        <h3 className="text-xs font-semibold text-slate-400 uppercase tracking-wide">Meters</h3>
        <div className="space-y-3">
          <div className="space-y-2">
            <label className="text-sm text-slate-300">LUFS Integration</label>
            <select className="w-full px-3 py-2 bg-slate-800 border border-slate-700 rounded-md text-sm text-slate-300 focus:outline-none focus:border-emerald-500">
              <option>Momentary (400ms)</option>
              <option>Short-term (3s)</option>
              <option>Integrated</option>
            </select>
          </div>

          <div className="space-y-2">
            <label className="text-sm text-slate-300">Peak Hold Time</label>
            <select className="w-full px-3 py-2 bg-slate-800 border border-slate-700 rounded-md text-sm text-slate-300 focus:outline-none focus:border-emerald-500">
              <option>500ms</option>
              <option>1 second</option>
              <option>2 seconds</option>
              <option>Infinite</option>
            </select>
          </div>
        </div>
      </div>

      {/* Export Options */}
      <div className="space-y-2">
        <h3 className="text-xs font-semibold text-slate-400 uppercase tracking-wide">Export</h3>
        <div className="space-y-3">
          <div className="space-y-2">
            <label className="text-sm text-slate-300">Report Format</label>
            <select className="w-full px-3 py-2 bg-slate-800 border border-slate-700 rounded-md text-sm text-slate-300 focus:outline-none focus:border-emerald-500">
              <option>PDF Report</option>
              <option>CSV Data</option>
              <option>JSON Data</option>
              <option>PNG Images</option>
            </select>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-slate-300">Include Waveforms</span>
            <button className="w-8 h-4 bg-emerald-500 rounded-full">
              <div className="w-3 h-3 bg-white rounded-full translate-x-4" />
            </button>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-slate-300">Include Spectrograms</span>
            <button className="w-8 h-4 bg-emerald-500 rounded-full">
              <div className="w-3 h-3 bg-white rounded-full translate-x-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Keyboard Shortcuts */}
      <div className="space-y-2">
        <h3 className="text-xs font-semibold text-slate-400 uppercase tracking-wide">Shortcuts</h3>
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-slate-300">Play/Pause</span>
            <span className="text-slate-500 font-mono">Space</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-slate-300">Crossfade A+B</span>
            <span className="text-slate-500 font-mono">Tab</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-slate-300">Toggle Sidebar</span>
            <span className="text-slate-500 font-mono">Ctrl+B</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-slate-300">Analysis Panel</span>
            <span className="text-slate-500 font-mono">Ctrl+Shift+A</span>
          </div>
        </div>
      </div>
    </div>
  );
}

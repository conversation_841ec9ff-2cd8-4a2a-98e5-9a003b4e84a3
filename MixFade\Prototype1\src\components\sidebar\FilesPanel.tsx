import React from 'react';
import { File, Upload } from 'lucide-react';

export function FilesPanel() {
  // Mock data - replace with real data later
  const recentFiles = [
    { name: 'Summer Mix 2024.mp3', path: '/projects/summer-mix.mp3', size: '8.2 MB', modified: '2 hours ago' },
    { name: 'Bass Drop Test.wav', path: '/samples/bass-drop.wav', size: '15.1 MB', modified: '1 day ago' },
    { name: 'Vocal Sample.aiff', path: '/vocals/sample-01.aiff', size: '12.8 MB', modified: '3 days ago' },
    { name: 'Reference Track.mp3', path: '/references/ref-01.mp3', size: '9.1 MB', modified: '1 week ago' },
    { name: 'Demo Master.wav', path: '/masters/demo.wav', size: '42.3 MB', modified: '2 weeks ago' },
  ];

  return (
    <div className="p-4 space-y-6">
      {/* Quick Load Actions */}
      <div className="space-y-2">
        <h3 className="text-xs font-semibold text-slate-400 uppercase tracking-wide">Quick Load</h3>
        <div className="space-y-1">
          <button className="w-full flex items-center space-x-3 px-3 py-2 text-sm text-slate-300 hover:bg-slate-800 rounded-md transition-colors">
            <Upload size={16} className="text-emerald-500" />
            <span>Load to A</span>
          </button>
          <button className="w-full flex items-center space-x-3 px-3 py-2 text-sm text-slate-300 hover:bg-slate-800 rounded-md transition-colors">
            <Upload size={16} className="text-purple-500" />
            <span>Load to B</span>
          </button>
        </div>
      </div>

      {/* Recent Files */}
      <div className="space-y-2">
        <h3 className="text-xs font-semibold text-slate-400 uppercase tracking-wide">Recent Files</h3>
        <div className="space-y-1">
          {recentFiles.map((file, index) => (
            <div
              key={index}
              className="flex items-center space-x-3 px-3 py-2 text-sm hover:bg-slate-800 rounded-md cursor-pointer transition-colors group"
            >
              <File size={16} className="text-slate-400 group-hover:text-slate-300" />
              <div className="flex-1 min-w-0">
                <p className="text-slate-300 truncate">{file.name}</p>
                <p className="text-xs text-slate-500">{file.size} • {file.modified}</p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

import React from 'react';
import { Book, Video, MessageCircle, ExternalLink, Keyboard, Info } from 'lucide-react';

export function HelpPanel() {
  const helpSections = [
    {
      title: 'Getting Started',
      items: [
        'Quick Start Guide',
        'Basic Mixing Tutorial',
        'Importing Your Music',
        'Understanding the Interface'
      ]
    },
    {
      title: 'Advanced Features',
      items: [
        'Crossfading Techniques',
        'Audio Effects Guide',
        'Keyboard Shortcuts',
        'Project Management'
      ]
    }
  ];

  return (
    <div className="p-4 space-y-6">
      {/* Quick Help */}
      <div className="space-y-2">
        <h3 className="text-xs font-semibold text-slate-400 uppercase tracking-wide">Quick Help</h3>
        <div className="space-y-1">
          <button className="w-full flex items-center space-x-3 px-3 py-2 text-sm text-slate-300 hover:bg-slate-800 rounded-md transition-colors">
            <Keyboard size={16} className="text-blue-500" />
            <span>Keyboard Shortcuts</span>
          </button>
          <button className="w-full flex items-center space-x-3 px-3 py-2 text-sm text-slate-300 hover:bg-slate-800 rounded-md transition-colors">
            <Video size={16} className="text-red-500" />
            <span>Video Tutorials</span>
          </button>
          <button className="w-full flex items-center space-x-3 px-3 py-2 text-sm text-slate-300 hover:bg-slate-800 rounded-md transition-colors">
            <Book size={16} className="text-emerald-500" />
            <span>User Manual</span>
          </button>
        </div>
      </div>

      {/* Documentation */}
      <div className="space-y-2">
        <h3 className="text-xs font-semibold text-slate-400 uppercase tracking-wide">Documentation</h3>
        <div className="space-y-3">
          {helpSections.map((section, sectionIndex) => (
            <div key={sectionIndex} className="space-y-2">
              <h4 className="text-sm font-medium text-slate-300">{section.title}</h4>
              <div className="space-y-1 ml-2">
                {section.items.map((item, itemIndex) => (
                  <button
                    key={itemIndex}
                    className="w-full flex items-center justify-between px-2 py-1 text-sm text-slate-400 hover:text-slate-300 hover:bg-slate-800 rounded transition-colors"
                  >
                    <span>{item}</span>
                    <ExternalLink size={12} className="text-slate-500" />
                  </button>
                ))}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Support */}
      <div className="space-y-2">
        <h3 className="text-xs font-semibold text-slate-400 uppercase tracking-wide">Support</h3>
        <div className="space-y-1">
          <button className="w-full flex items-center space-x-3 px-3 py-2 text-sm text-slate-300 hover:bg-slate-800 rounded-md transition-colors">
            <MessageCircle size={16} className="text-blue-500" />
            <span>Contact Support</span>
          </button>
          <button className="w-full flex items-center space-x-3 px-3 py-2 text-sm text-slate-300 hover:bg-slate-800 rounded-md transition-colors">
            <ExternalLink size={16} className="text-purple-500" />
            <span>Community Forum</span>
          </button>
          <button className="w-full flex items-center space-x-3 px-3 py-2 text-sm text-slate-300 hover:bg-slate-800 rounded-md transition-colors">
            <Info size={16} className="text-yellow-500" />
            <span>Report a Bug</span>
          </button>
        </div>
      </div>

      {/* About */}
      <div className="space-y-2">
        <h3 className="text-xs font-semibold text-slate-400 uppercase tracking-wide">About</h3>
        <div className="p-3 bg-slate-800 rounded-lg">
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span className="text-slate-400">Version</span>
              <span className="text-slate-300">1.0.0</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-slate-400">Build</span>
              <span className="text-slate-300">2024.01.15</span>
            </div>
            <div className="flex items-center justify-between text-sm">
              <span className="text-slate-400">Platform</span>
              <span className="text-slate-300">Electron</span>
            </div>
          </div>
          <div className="mt-3 pt-3 border-t border-slate-700">
            <p className="text-xs text-slate-500 text-center">
              MixFade - Professional DJ Mixing Software
            </p>
          </div>
        </div>
      </div>

      {/* Keyboard Shortcuts Reference */}
      <div className="space-y-2">
        <h3 className="text-xs font-semibold text-slate-400 uppercase tracking-wide">Shortcuts</h3>
        <div className="space-y-1 text-sm">
          <div className="flex items-center justify-between">
            <span className="text-slate-300">Play/Pause</span>
            <span className="text-slate-500 font-mono text-xs">Space</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-slate-300">Crossfade A+B</span>
            <span className="text-slate-500 font-mono text-xs">Tab</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-slate-300">Toggle Sidebar</span>
            <span className="text-slate-500 font-mono text-xs">Ctrl+B</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-slate-300">Files Panel</span>
            <span className="text-slate-500 font-mono text-xs">Ctrl+Shift+E</span>
          </div>
          <div className="flex items-center justify-between">
            <span className="text-slate-300">Mixer Panel</span>
            <span className="text-slate-500 font-mono text-xs">Ctrl+Shift+M</span>
          </div>
        </div>
      </div>
    </div>
  );
}

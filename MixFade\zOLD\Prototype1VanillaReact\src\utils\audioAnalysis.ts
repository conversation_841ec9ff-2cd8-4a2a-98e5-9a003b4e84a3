export interface AudioLevels {
  left: number;
  right: number;
  leftRms: number;
  rightRms: number;
  rms: number;
  lufs: number;
  leftLufs: number;  // Add individual L channel LUFS
  rightLufs: number; // Add individual R channel LUFS
}

export interface SmoothedAudioLevels extends AudioLevels {
  leftRmsSmoothed: number;
  rightRmsSmoothed: number;
  rmsSmoothed: number;
  lufsSmoothed: number;
  leftLufsSmoothed: number;  // Add smoothed L LUFS
  rightLufsSmoothed: number; // Add smoothed R LUFS
}

export class RMSAverager {
  private leftRmsHistory: number[] = [];
  private rightRmsHistory: number[] = [];
  private combinedRmsHistory: number[] = [];
  private lufsHistory: number[] = [];
  private leftLufsHistory: number[] = [];  // Add L LUFS history
  private rightLufsHistory: number[] = []; // Add R LUFS history
  private readonly windowSize: number;
  private readonly updateInterval: number;
  private lastUpdateTime: number = 0;

  constructor(windowSizeMs: number = 300, updateIntervalMs: number = 50) {
    // Calculate how many samples we need for the time window
    // Assuming ~60fps updates, we get about 16.67ms per frame
    this.windowSize = Math.max(1, Math.floor(windowSizeMs / 16.67));
    this.updateInterval = updateIntervalMs;
  }

  addSample(leftRms: number, rightRms: number, combinedRms: number, lufs: number, leftLufs: number, rightLufs: number): boolean {
    const now = performance.now();
    
    // Only update at our specified interval to prevent over-smoothing
    if (now - this.lastUpdateTime < this.updateInterval) {
      return false;
    }
    
    this.lastUpdateTime = now;
    
    // Add new samples
    this.leftRmsHistory.push(leftRms);
    this.rightRmsHistory.push(rightRms);
    this.combinedRmsHistory.push(combinedRms);
    this.lufsHistory.push(lufs);
    this.leftLufsHistory.push(leftLufs);
    this.rightLufsHistory.push(rightLufs);
    
    // Maintain window size
    if (this.leftRmsHistory.length > this.windowSize) {
      this.leftRmsHistory.shift();
      this.rightRmsHistory.shift();
      this.combinedRmsHistory.shift();
      this.lufsHistory.shift();
      this.leftLufsHistory.shift();
      this.rightLufsHistory.shift();
    }
    
    return true;
  }

  getSmoothedValues(): {
    leftRmsSmoothed: number;
    rightRmsSmoothed: number;
    rmsSmoothed: number;
    lufsSmoothed: number;
    leftLufsSmoothed: number;
    rightLufsSmoothed: number;
  } {
    if (this.leftRmsHistory.length === 0) {
      return {
        leftRmsSmoothed: 0,
        rightRmsSmoothed: 0,
        rmsSmoothed: 0,
        lufsSmoothed: -70,
        leftLufsSmoothed: -70,
        rightLufsSmoothed: -70
      };
    }

    // Use RMS averaging for RMS values (more accurate than simple mean)
    const leftRmsSmoothed = Math.sqrt(
      this.leftRmsHistory.reduce((sum, val) => sum + val * val, 0) / this.leftRmsHistory.length
    );
    
    const rightRmsSmoothed = Math.sqrt(
      this.rightRmsHistory.reduce((sum, val) => sum + val * val, 0) / this.rightRmsHistory.length
    );
    
    const rmsSmoothed = Math.sqrt(
      this.combinedRmsHistory.reduce((sum, val) => sum + val * val, 0) / this.combinedRmsHistory.length
    );

    // Use simple average for LUFS (already logarithmic)
    const lufsSmoothed = this.lufsHistory.reduce((sum, val) => sum + val, 0) / this.lufsHistory.length;
    const leftLufsSmoothed = this.leftLufsHistory.reduce((sum, val) => sum + val, 0) / this.leftLufsHistory.length;
    const rightLufsSmoothed = this.rightLufsHistory.reduce((sum, val) => sum + val, 0) / this.rightLufsHistory.length;

    return {
      leftRmsSmoothed,
      rightRmsSmoothed,
      rmsSmoothed,
      lufsSmoothed,
      leftLufsSmoothed,
      rightLufsSmoothed
    };
  }

  reset(): void {
    this.leftRmsHistory = [];
    this.rightRmsHistory = [];
    this.combinedRmsHistory = [];
    this.lufsHistory = [];
    this.leftLufsHistory = [];
    this.rightLufsHistory = [];
    this.lastUpdateTime = 0;
  }
}

export const AudioUtils = {
  linearToDb: (linear: number): number => {
    if (linear <= 0) return -60;
    return Math.max(-60, 20 * Math.log10(linear));
  },

  dbToLinear: (db: number): number => {
    if (db <= -60) return 0;
    return Math.pow(10, db / 20);
  },

  rmsToDb: (rms: number): number => {
    if (rms <= 0) return -60;
    return Math.max(-60, 20 * Math.log10(rms));
  },

  calculateRMS: (samples: Float32Array): number => {
    let sum = 0;
    for (let i = 0; i < samples.length; i++) {
      sum += samples[i] * samples[i];
    }
    return Math.sqrt(sum / samples.length);
  },

  // Calculate RMS for stereo channels separately
  calculateStereoRMS: (leftSamples: Float32Array, rightSamples: Float32Array): { leftRms: number, rightRms: number, combinedRms: number } => {
    const leftRms = AudioUtils.calculateRMS(leftSamples);
    const rightRms = AudioUtils.calculateRMS(rightSamples);
    
    // Combined RMS (L+R)
    let combinedSum = 0;
    const totalSamples = leftSamples.length + rightSamples.length;
    
    for (let i = 0; i < leftSamples.length; i++) {
      combinedSum += leftSamples[i] * leftSamples[i];
    }
    for (let i = 0; i < rightSamples.length; i++) {
      combinedSum += rightSamples[i] * rightSamples[i];
    }
    
    const combinedRms = Math.sqrt(combinedSum / totalSamples);
    
    return { leftRms, rightRms, combinedRms };
  },

  estimateLUFS: (rms: number): number => {
    // Simple LUFS estimation based on RMS
    if (rms <= 0) return -70;
    const db = AudioUtils.rmsToDb(rms);
    // Rough conversion to LUFS (this is a simplified approximation)
    return Math.max(-70, Math.min(0, db - 3));
  },

  // Calculate individual channel LUFS
  estimateChannelLUFS: (leftRms: number, rightRms: number): { leftLufs: number, rightLufs: number } => {
    const leftLufs = AudioUtils.estimateLUFS(leftRms);
    const rightLufs = AudioUtils.estimateLUFS(rightRms);
    return { leftLufs, rightLufs };
  }
};
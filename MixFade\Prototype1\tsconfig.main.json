{"extends": "./tsconfig.json", "compilerOptions": {"outDir": "dist/main", "rootDir": "src/main", "module": "CommonJS", "moduleResolution": "node", "target": "ES2020", "strict": false, "noImplicitAny": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": false, "noEmit": false, "sourceMap": true, "declaration": false, "types": ["node"], "typeRoots": ["./node_modules/@types"]}, "include": ["src/main/**/*"], "exclude": ["node_modules", "**/*.spec.ts"]}
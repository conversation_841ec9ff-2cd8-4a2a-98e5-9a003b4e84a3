import React from 'react';
import { Search, Music, Folder, Clock, Heart, TrendingUp } from 'lucide-react';

export function LibraryPanel() {
  const categories = [
    { name: 'All Music', count: 1247, icon: Music },
    { name: 'Recently Added', count: 23, icon: Clock },
    { name: 'Favorites', count: 89, icon: Heart },
    { name: 'Trending', count: 45, icon: TrendingUp },
  ];

  const genres = [
    'House', 'Techno', 'Hip Hop', 'Pop', 'Rock', 'Jazz', 'Classical', 'Electronic'
  ];

  const recentTracks = [
    { title: 'Summer Vibes', artist: 'DJ Cool', duration: '3:45', bpm: 128 },
    { title: 'Night Drive', artist: 'Synthwave Pro', duration: '4:12', bpm: 110 },
    { title: 'Bass Drop', artist: 'Heavy Beats', duration: '2:58', bpm: 140 },
  ];

  return (
    <div className="p-4 space-y-6">
      {/* Search */}
      <div className="space-y-2">
        <div className="relative">
          <Search size={16} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400" />
          <input
            type="text"
            placeholder="Search library..."
            className="w-full pl-10 pr-4 py-2 bg-slate-800 border border-slate-700 rounded-md text-sm text-slate-300 placeholder-slate-500 focus:outline-none focus:border-emerald-500"
          />
        </div>
      </div>

      {/* Categories */}
      <div className="space-y-2">
        <h3 className="text-xs font-semibold text-slate-400 uppercase tracking-wide">Library</h3>
        <div className="space-y-1">
          {categories.map((category, index) => (
            <button
              key={index}
              className="w-full flex items-center justify-between px-3 py-2 text-sm text-slate-300 hover:bg-slate-800 rounded-md transition-colors"
            >
              <div className="flex items-center space-x-3">
                <category.icon size={16} className="text-slate-400" />
                <span>{category.name}</span>
              </div>
              <span className="text-xs text-slate-500">{category.count}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Genres */}
      <div className="space-y-2">
        <h3 className="text-xs font-semibold text-slate-400 uppercase tracking-wide">Genres</h3>
        <div className="flex flex-wrap gap-1">
          {genres.map((genre, index) => (
            <button
              key={index}
              className="px-2 py-1 text-xs text-slate-300 bg-slate-800 hover:bg-slate-700 rounded transition-colors"
            >
              {genre}
            </button>
          ))}
        </div>
      </div>

      {/* Recent Tracks */}
      <div className="space-y-2">
        <h3 className="text-xs font-semibold text-slate-400 uppercase tracking-wide">Recent</h3>
        <div className="space-y-1">
          {recentTracks.map((track, index) => (
            <div
              key={index}
              className="p-3 bg-slate-800 rounded-md hover:bg-slate-700 cursor-pointer transition-colors"
            >
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1">
                  <p className="text-sm text-slate-300 truncate">{track.title}</p>
                  <p className="text-xs text-slate-500">{track.artist}</p>
                </div>
                <div className="text-right ml-2">
                  <p className="text-xs text-slate-400">{track.duration}</p>
                  <p className="text-xs text-slate-500">{track.bpm} BPM</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="space-y-2">
        <h3 className="text-xs font-semibold text-slate-400 uppercase tracking-wide">Actions</h3>
        <div className="space-y-1">
          <button className="w-full flex items-center space-x-3 px-3 py-2 text-sm text-slate-300 hover:bg-slate-800 rounded-md transition-colors">
            <Folder size={16} className="text-blue-500" />
            <span>Import Folder</span>
          </button>
          <button className="w-full flex items-center space-x-3 px-3 py-2 text-sm text-slate-300 hover:bg-slate-800 rounded-md transition-colors">
            <Music size={16} className="text-emerald-500" />
            <span>Scan for Music</span>
          </button>
        </div>
      </div>
    </div>
  );
}

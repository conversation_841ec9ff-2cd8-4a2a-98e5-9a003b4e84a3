import React from 'react';
import { Zap, RotateCcw, Save, Star } from 'lucide-react';

export function EffectsPanel() {
  const effects = [
    { name: 'Reverb', enabled: true, intensity: 35 },
    { name: 'Delay', enabled: false, intensity: 0 },
    { name: 'Chorus', enabled: true, intensity: 20 },
    { name: 'Distortion', enabled: false, intensity: 0 },
    { name: 'Filter', enabled: true, intensity: 60 },
  ];

  const presets = [
    'Club Sound', 'Radio Ready', 'Warm Vintage', 'Crystal Clear', 'Bass Boost'
  ];

  return (
    <div className="p-4 space-y-6">
      {/* Presets */}
      <div className="space-y-2">
        <h3 className="text-xs font-semibold text-slate-400 uppercase tracking-wide">Presets</h3>
        <div className="space-y-1">
          {presets.map((preset, index) => (
            <button
              key={index}
              className="w-full flex items-center space-x-3 px-3 py-2 text-sm text-slate-300 hover:bg-slate-800 rounded-md transition-colors text-left"
            >
              <Star size={14} className="text-yellow-500" />
              <span>{preset}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Effects */}
      <div className="space-y-2">
        <h3 className="text-xs font-semibold text-slate-400 uppercase tracking-wide">Effects</h3>
        <div className="space-y-3">
          {effects.map((effect, index) => (
            <div key={index} className="p-3 bg-slate-800 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-slate-300">{effect.name}</span>
                <button
                  className={`w-8 h-4 rounded-full transition-colors ${
                    effect.enabled ? 'bg-emerald-500' : 'bg-slate-600'
                  }`}
                >
                  <div
                    className={`w-3 h-3 bg-white rounded-full transition-transform ${
                      effect.enabled ? 'translate-x-4' : 'translate-x-0.5'
                    }`}
                  />
                </button>
              </div>
              <div className="space-y-1">
                <div className="flex items-center justify-between">
                  <span className="text-xs text-slate-500">Intensity</span>
                  <span className="text-xs text-slate-400">{effect.intensity}%</span>
                </div>
                <input
                  type="range"
                  min="0"
                  max="100"
                  value={effect.intensity}
                  disabled={!effect.enabled}
                  className="w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer slider"
                />
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="space-y-2">
        <h3 className="text-xs font-semibold text-slate-400 uppercase tracking-wide">Actions</h3>
        <div className="grid grid-cols-2 gap-2">
          <button className="flex items-center justify-center space-x-2 px-3 py-2 text-sm text-slate-300 bg-slate-800 hover:bg-slate-700 rounded-md transition-colors">
            <RotateCcw size={14} />
            <span>Reset</span>
          </button>
          <button className="flex items-center justify-center space-x-2 px-3 py-2 text-sm text-slate-300 bg-slate-800 hover:bg-slate-700 rounded-md transition-colors">
            <Save size={14} />
            <span>Save</span>
          </button>
        </div>
      </div>
    </div>
  );
}

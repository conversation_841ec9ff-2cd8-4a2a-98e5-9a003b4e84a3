import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Play, Pause, Volume2, VolumeX } from 'lucide-react';
import { AudioLevels, AudioUtils } from '../utils/audioAnalysis';

interface AudioMetadata {
  sampleRate: number;
  channels: number;
  bitDepth: string;
  format: string;
  duration: number;
}

interface WaveformPlayerProps {
  file: File;
  color: 'green' | 'purple';
  label: string;
  onPlayStateChange?: (isPlaying: boolean) => void;
  onAudioLevels?: (levels: AudioLevels) => void;
  crossfadeVolume?: number; // Volume from crossfade control (0-1)
}

export function WaveformPlayer({ 
  file, 
  color, 
  label, 
  onPlayStateChange, 
  onAudioLevels,
  crossfadeVolume = 1
}: WaveformPlayerProps) {
  const leftCanvasRef = useRef<HTMLCanvasElement>(null);
  const rightCanvasRef = useRef<HTMLCanvasElement>(null);
  const audioRef = useRef<HTMLAudioElement>(null);
  const audioContext = useRef<AudioContext | null>(null);
  const sourceNode = useRef<MediaElementAudioSourceNode | null>(null);
  const analyserNode = useRef<AnalyserNode | null>(null);
  const gainNode = useRef<GainNode | null>(null);
  const splitterNode = useRef<ChannelSplitterNode | null>(null);
  const leftAnalyser = useRef<AnalyserNode | null>(null);
  const rightAnalyser = useRef<AnalyserNode | null>(null);
  const animationFrame = useRef<number | null>(null);
  const leftWaveformData = useRef<Float32Array | null>(null);
  const rightWaveformData = useRef<Float32Array | null>(null);
  const fileUrl = useRef<string | null>(null);
  const currentFile = useRef<File | null>(null);
  const isAudioContextSetup = useRef<boolean>(false);
  
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1.0);
  const [isMuted, setIsMuted] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [canPlay, setCanPlay] = useState(false);
  const [audioMetadata, setAudioMetadata] = useState<AudioMetadata | null>(null);

  const colorConfig = {
    green: {
      waveColor: '#10b981',
      bgColor: 'bg-emerald-500',
      hoverColor: 'hover:bg-emerald-600',
      textColor: 'text-emerald-400'
    },
    purple: {
      waveColor: '#8b5cf6',
      bgColor: 'bg-purple-500',
      hoverColor: 'hover:bg-purple-600',
      textColor: 'text-purple-400'
    }
  };

  const config = colorConfig[color];

  // Extract audio metadata
  const extractAudioMetadata = useCallback(async (audioBuffer: AudioBuffer): Promise<AudioMetadata> => {
    const fileExtension = file.name.split('.').pop()?.toLowerCase() || '';
    
    // Estimate bit depth based on file type and size
    const estimateBitDepth = () => {
      const fileSizeBytes = file.size;
      const durationSeconds = audioBuffer.duration;
      const channels = audioBuffer.numberOfChannels;
      const sampleRate = audioBuffer.sampleRate;
      
      // Calculate approximate bit depth from file size
      const totalSamples = durationSeconds * sampleRate * channels;
      const bitsPerSample = (fileSizeBytes * 8) / totalSamples;
      
      if (fileExtension === 'mp3' || fileExtension === 'aac' || fileExtension === 'm4a') {
        // For compressed formats, estimate bitrate instead
        const bitrate = Math.round((fileSizeBytes * 8) / durationSeconds / 1000);
        return `${bitrate}kbps`;
      } else if (bitsPerSample > 20) {
        return '24-bit';
      } else if (bitsPerSample > 12) {
        return '16-bit';
      } else {
        return '8-bit';
      }
    };

    // Determine format
    const getFormat = () => {
      switch (fileExtension) {
        case 'wav': return 'WAV';
        case 'mp3': return 'MP3';
        case 'flac': return 'FLAC';
        case 'aiff':
        case 'aif': return 'AIFF';
        case 'ogg': return 'OGG';
        case 'm4a': return 'M4A';
        case 'aac': return 'AAC';
        case 'wma': return 'WMA';
        default: return fileExtension.toUpperCase();
      }
    };

    return {
      sampleRate: audioBuffer.sampleRate,
      channels: audioBuffer.numberOfChannels,
      bitDepth: estimateBitDepth(),
      format: getFormat(),
      duration: audioBuffer.duration
    };
  }, [file]);

  // Setup audio context and routing with stereo analysis
  const setupAudioContext = useCallback(async () => {
    const audio = audioRef.current;
    if (!audio || isAudioContextSetup.current) return;

    try {
      if (!audioContext.current) {
        audioContext.current = new (window.AudioContext || (window as any).webkitAudioContext)();
      }

      if (audioContext.current.state === 'suspended') {
        await audioContext.current.resume();
      }

      if (!sourceNode.current) {
        sourceNode.current = audioContext.current.createMediaElementSource(audio);
        analyserNode.current = audioContext.current.createAnalyser();
        gainNode.current = audioContext.current.createGain();
        
        // Create stereo analysis setup
        splitterNode.current = audioContext.current.createChannelSplitter(2);
        leftAnalyser.current = audioContext.current.createAnalyser();
        rightAnalyser.current = audioContext.current.createAnalyser();
        
        // Configure analysers
        analyserNode.current.fftSize = 2048;
        leftAnalyser.current.fftSize = 2048;
        rightAnalyser.current.fftSize = 2048;
        
        // Connect: source -> gain -> splitter -> individual analysers
        sourceNode.current.connect(gainNode.current);
        gainNode.current.connect(splitterNode.current);
        gainNode.current.connect(analyserNode.current);
        gainNode.current.connect(audioContext.current.destination);
        
        // Connect stereo channels to separate analysers
        splitterNode.current.connect(leftAnalyser.current, 0);
        splitterNode.current.connect(rightAnalyser.current, 1);
        
        // Set initial gain based on crossfade
        const baseVolume = isMuted ? 0 : volume;
        const finalVolume = baseVolume * (crossfadeVolume || 1);
        gainNode.current.gain.setValueAtTime(finalVolume, audioContext.current.currentTime);
        
        isAudioContextSetup.current = true;
        console.log('Stereo audio context setup complete');
      }
    } catch (error) {
      console.warn('Audio context setup failed:', error);
    }
  }, [crossfadeVolume, volume, isMuted]);

  // Update audio volume based on crossfade - COMPLETE CUTOFF when volume is 0
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    // Use both audio element volume AND gain node for complete control
    if (crossfadeVolume === 0) {
      // Complete silence - use audio element volume for immediate cutoff
      audio.volume = 0;
      if (gainNode.current) {
        gainNode.current.gain.setValueAtTime(0, audioContext.current?.currentTime || 0);
      }
    } else {
      // Normal volume control
      const baseVolume = isMuted ? 0 : volume;
      const finalVolume = baseVolume * crossfadeVolume;
      
      // Set audio element volume
      audio.volume = finalVolume;
      
      // Also set gain node if available
      if (gainNode.current && audioContext.current) {
        try {
          const currentTime = audioContext.current.currentTime;
          gainNode.current.gain.cancelScheduledValues(currentTime);
          gainNode.current.gain.setValueAtTime(finalVolume, currentTime);
        } catch (error) {
          console.warn('Gain node update failed:', error);
        }
      }
    }
  }, [crossfadeVolume, volume, isMuted]);

  // Generate separate L/R waveform data from audio buffer
  const generateStereoWaveformData = useCallback(async (audioBuffer: AudioBuffer) => {
    const width = 800;
    const samples = audioBuffer.length;
    const samplesPerPixel = Math.floor(samples / width);
    
    // Get channel data
    const leftChannelData = audioBuffer.getChannelData(0);
    const rightChannelData = audioBuffer.numberOfChannels > 1 ? audioBuffer.getChannelData(1) : leftChannelData;
    
    // Process left channel
    const leftProcessedData = new Float32Array(width * 2);
    for (let x = 0; x < width; x++) {
      const startSample = x * samplesPerPixel;
      const endSample = Math.min(startSample + samplesPerPixel, samples);
      
      let min = 0;
      let max = 0;
      
      for (let i = startSample; i < endSample; i++) {
        const sample = leftChannelData[i];
        if (sample < min) min = sample;
        if (sample > max) max = sample;
      }
      
      leftProcessedData[x * 2] = min;
      leftProcessedData[x * 2 + 1] = max;
    }
    
    // Process right channel
    const rightProcessedData = new Float32Array(width * 2);
    for (let x = 0; x < width; x++) {
      const startSample = x * samplesPerPixel;
      const endSample = Math.min(startSample + samplesPerPixel, samples);
      
      let min = 0;
      let max = 0;
      
      for (let i = startSample; i < endSample; i++) {
        const sample = rightChannelData[i];
        if (sample < min) min = sample;
        if (sample > max) max = sample;
      }
      
      rightProcessedData[x * 2] = min;
      rightProcessedData[x * 2 + 1] = max;
    }
    
    return { leftProcessedData, rightProcessedData };
  }, []);

  // Draw waveform on canvas for a specific channel
  const drawChannelWaveform = useCallback((canvas: HTMLCanvasElement, waveformData: Float32Array, channelLabel: string) => {
    if (!canvas || !waveformData) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const width = canvas.width;
    const height = canvas.height;
    const centerY = height / 2;

    // Clear canvas
    ctx.fillStyle = '#0f172a';
    ctx.fillRect(0, 0, width, height);

    // Draw center line
    ctx.strokeStyle = '#334155';
    ctx.lineWidth = 1;
    ctx.setLineDash([3, 3]);
    ctx.beginPath();
    ctx.moveTo(0, centerY);
    ctx.lineTo(width, centerY);
    ctx.stroke();
    ctx.setLineDash([]);

    // Create gradient with crossfade opacity - more dramatic dimming when muted
    const opacity = crossfadeVolume === 0 ? 0.1 : Math.max(0.3, crossfadeVolume || 1);
    const fillGradient = ctx.createLinearGradient(0, 0, 0, height);
    fillGradient.addColorStop(0, config.waveColor + Math.round(opacity * 64).toString(16).padStart(2, '0'));
    fillGradient.addColorStop(0.5, config.waveColor + Math.round(opacity * 32).toString(16).padStart(2, '0'));
    fillGradient.addColorStop(1, config.waveColor + Math.round(opacity * 64).toString(16).padStart(2, '0'));

    // Draw waveform
    ctx.strokeStyle = config.waveColor + Math.round(opacity * 255).toString(16).padStart(2, '0');
    ctx.lineWidth = 1.5;
    ctx.fillStyle = fillGradient;

    // Draw positive peaks
    ctx.beginPath();
    for (let x = 0; x < width; x++) {
      const max = waveformData[x * 2 + 1];
      const y = centerY - (max * centerY * 0.9);
      if (x === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    }
    ctx.stroke();

    // Fill area
    ctx.beginPath();
    for (let x = 0; x < width; x++) {
      const max = waveformData[x * 2 + 1];
      const y = centerY - (max * centerY * 0.9);
      if (x === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    }
    for (let x = width - 1; x >= 0; x--) {
      const min = waveformData[x * 2];
      const y = centerY - (min * centerY * 0.9);
      ctx.lineTo(x, y);
    }
    ctx.closePath();
    ctx.fill();

    // Draw playback position
    if (duration > 0 && currentTime > 0) {
      const playbackX = (currentTime / duration) * width;
      ctx.strokeStyle = crossfadeVolume === 0 ? '#ffffff40' : '#ffffff';
      ctx.lineWidth = 2;
      if (crossfadeVolume > 0) {
        ctx.shadowColor = '#ffffff';
        ctx.shadowBlur = 6;
      }
      ctx.beginPath();
      ctx.moveTo(playbackX, 0);
      ctx.lineTo(playbackX, height);
      ctx.stroke();
      ctx.shadowBlur = 0;
    }

    // Draw channel label
    ctx.fillStyle = crossfadeVolume === 0 ? '#ffffff40' : '#ffffff80';
    ctx.font = 'bold 12px Inter';
    ctx.fillText(channelLabel, 8, 20);
  }, [currentTime, duration, config.waveColor, crossfadeVolume]);

  // Draw both waveforms
  const drawWaveforms = useCallback(() => {
    if (leftCanvasRef.current && leftWaveformData.current) {
      drawChannelWaveform(leftCanvasRef.current, leftWaveformData.current, 'L');
    }
    if (rightCanvasRef.current && rightWaveformData.current) {
      drawChannelWaveform(rightCanvasRef.current, rightWaveformData.current, 'R');
    }
  }, [drawChannelWaveform]);

  // Cleanup function
  const cleanup = useCallback(() => {
    console.log('Cleaning up audio resources...');
    
    if (animationFrame.current) {
      cancelAnimationFrame(animationFrame.current);
      animationFrame.current = null;
    }
    
    if (sourceNode.current) {
      try {
        sourceNode.current.disconnect();
      } catch (e) {}
      sourceNode.current = null;
    }
    
    if (gainNode.current) {
      try {
        gainNode.current.disconnect();
      } catch (e) {}
      gainNode.current = null;
    }

    if (splitterNode.current) {
      try {
        splitterNode.current.disconnect();
      } catch (e) {}
      splitterNode.current = null;
    }

    if (leftAnalyser.current) {
      try {
        leftAnalyser.current.disconnect();
      } catch (e) {}
      leftAnalyser.current = null;
    }

    if (rightAnalyser.current) {
      try {
        rightAnalyser.current.disconnect();
      } catch (e) {}
      rightAnalyser.current = null;
    }
    
    if (audioContext.current && audioContext.current.state !== 'closed') {
      try {
        audioContext.current.close();
      } catch (e) {}
      audioContext.current = null;
    }
    
    analyserNode.current = null;
    isAudioContextSetup.current = false;
  }, []);

  // Initialize audio when file changes
  useEffect(() => {
    // Only initialize if file actually changed
    if (currentFile.current === file) return;
    
    console.log('Initializing audio for:', file.name);
    
    // Cleanup previous resources
    cleanup();
    
    // Reset state
    setIsPlaying(false);
    setCurrentTime(0);
    setDuration(0);
    setIsLoading(true);
    setError(null);
    setCanPlay(false);
    setAudioMetadata(null);
    
    currentFile.current = file;
    
    const initializeAudio = async () => {
      try {
        // Clean up old URL
        if (fileUrl.current) {
          URL.revokeObjectURL(fileUrl.current);
          fileUrl.current = null;
        }

        // Create new blob URL and keep reference
        fileUrl.current = URL.createObjectURL(file);
        console.log('Created object URL:', fileUrl.current);

        // Set up audio element
        if (audioRef.current) {
          audioRef.current.src = fileUrl.current;
          audioRef.current.volume = crossfadeVolume === 0 ? 0 : (isMuted ? 0 : volume);
          audioRef.current.preload = 'auto';
          audioRef.current.load();
        }

        // Decode audio for waveform and metadata
        console.log('Starting audio decode...');
        const arrayBuffer = await file.arrayBuffer();
        console.log('Got array buffer, size:', arrayBuffer.byteLength);
        
        const tempContext = new (window.AudioContext || (window as any).webkitAudioContext)();
        const audioBuffer = await tempContext.decodeAudioData(arrayBuffer);
        console.log('Audio decoded successfully');

        // Extract metadata
        const metadata = await extractAudioMetadata(audioBuffer);
        setAudioMetadata(metadata);
        console.log('Audio metadata:', metadata);

        await tempContext.close();

        // Generate stereo waveform data
        const { leftProcessedData, rightProcessedData } = await generateStereoWaveformData(audioBuffer);
        leftWaveformData.current = leftProcessedData;
        rightWaveformData.current = rightProcessedData;
        setDuration(audioBuffer.duration);

        // Draw initial waveforms
        setTimeout(() => {
          drawWaveforms();
          setIsLoading(false);
          console.log('Audio initialized successfully');
        }, 100);

      } catch (error) {
        console.error('Failed to initialize audio:', error);
        setError('Failed to load audio file');
        setIsLoading(false);
      }
    };

    initializeAudio();

    // Cleanup on unmount or file change
    return () => {
      cleanup();
      // Don't revoke URL here - let it persist for audio element
    };
  }, [file, cleanup, generateStereoWaveformData, drawWaveforms, crossfadeVolume, volume, isMuted, extractAudioMetadata]);

  // Set up audio event listeners
  useEffect(() => {
    const audio = audioRef.current;
    if (!audio) return;

    const handleLoadedMetadata = () => {
      console.log('Audio metadata loaded, duration:', audio.duration);
      setDuration(audio.duration);
    };

    const handleCanPlay = async () => {
      console.log('Audio can play');
      setCanPlay(true);
      setIsLoading(false);
      
      // Setup audio context when audio is ready
      await setupAudioContext();
    };

    const handleTimeUpdate = () => {
      setCurrentTime(audio.currentTime);
    };

    const handlePlay = async () => {
      console.log('Audio started playing');
      setIsPlaying(true);
      
      // Ensure audio context is setup
      await setupAudioContext();
    };

    const handlePause = () => {
      console.log('Audio paused');
      setIsPlaying(false);
    };

    const handleEnded = () => {
      console.log('Audio ended');
      setIsPlaying(false);
      setCurrentTime(0);
    };

    const handleError = () => {
      console.error('Audio error:', audio.error);
      setError('Audio playback error');
      setIsPlaying(false);
      setIsLoading(false);
    };

    audio.addEventListener('loadedmetadata', handleLoadedMetadata);
    audio.addEventListener('canplay', handleCanPlay);
    audio.addEventListener('timeupdate', handleTimeUpdate);
    audio.addEventListener('play', handlePlay);
    audio.addEventListener('pause', handlePause);
    audio.addEventListener('ended', handleEnded);
    audio.addEventListener('error', handleError);

    return () => {
      audio.removeEventListener('loadedmetadata', handleLoadedMetadata);
      audio.removeEventListener('canplay', handleCanPlay);
      audio.removeEventListener('timeupdate', handleTimeUpdate);
      audio.removeEventListener('play', handlePlay);
      audio.removeEventListener('pause', handlePause);
      audio.removeEventListener('ended', handleEnded);
      audio.removeEventListener('error', handleError);
    };
  }, [setupAudioContext]);

  // Redraw waveforms when time changes
  useEffect(() => {
    if (leftWaveformData.current && rightWaveformData.current && !isLoading) {
      drawWaveforms();
    }
  }, [currentTime, drawWaveforms, isLoading]);

  // Audio level monitoring with separate L/R RMS analysis and individual LUFS
  useEffect(() => {
    const updateLevels = () => {
      if (leftAnalyser.current && rightAnalyser.current && isPlaying && onAudioLevels) {
        // Get separate channel data
        const leftDataArray = new Uint8Array(leftAnalyser.current.frequencyBinCount);
        const rightDataArray = new Uint8Array(rightAnalyser.current.frequencyBinCount);
        
        leftAnalyser.current.getByteTimeDomainData(leftDataArray);
        rightAnalyser.current.getByteTimeDomainData(rightDataArray);
        
        // Convert to float samples
        const leftSamples = new Float32Array(leftDataArray.length);
        const rightSamples = new Float32Array(rightDataArray.length);
        
        for (let i = 0; i < leftDataArray.length; i++) {
          leftSamples[i] = (leftDataArray[i] - 128) / 128;
        }
        for (let i = 0; i < rightDataArray.length; i++) {
          rightSamples[i] = (rightDataArray[i] - 128) / 128;
        }
        
        // Calculate separate RMS values
        const { leftRms, rightRms, combinedRms } = AudioUtils.calculateStereoRMS(leftSamples, rightSamples);
        
        // Calculate peak levels (for visual meters)
        const leftPeak = Math.min(1, leftRms * 4);
        const rightPeak = Math.min(1, rightRms * 4);
        
        // Calculate LUFS for combined and individual channels
        const lufs = AudioUtils.estimateLUFS(combinedRms);
        const { leftLufs, rightLufs } = AudioUtils.estimateChannelLUFS(leftRms, rightRms);
        
        // Apply crossfade volume to reported levels
        const effectiveLeftPeak = crossfadeVolume === 0 ? 0 : leftPeak;
        const effectiveRightPeak = crossfadeVolume === 0 ? 0 : rightPeak;
        const effectiveLeftRms = crossfadeVolume === 0 ? 0 : leftRms;
        const effectiveRightRms = crossfadeVolume === 0 ? 0 : rightRms;
        const effectiveCombinedRms = crossfadeVolume === 0 ? 0 : combinedRms;
        const effectiveLufs = crossfadeVolume === 0 ? -70 : lufs;
        const effectiveLeftLufs = crossfadeVolume === 0 ? -70 : leftLufs;
        const effectiveRightLufs = crossfadeVolume === 0 ? -70 : rightLufs;
        
        onAudioLevels({
          left: effectiveLeftPeak,
          right: effectiveRightPeak,
          leftRms: effectiveLeftRms,
          rightRms: effectiveRightRms,
          rms: effectiveCombinedRms,
          lufs: effectiveLufs,
          leftLufs: effectiveLeftLufs,
          rightLufs: effectiveRightLufs
        });
      }
      
      if (isPlaying) {
        animationFrame.current = requestAnimationFrame(updateLevels);
      }
    };

    if (isPlaying) {
      animationFrame.current = requestAnimationFrame(updateLevels);
    } else {
      if (animationFrame.current) {
        cancelAnimationFrame(animationFrame.current);
        animationFrame.current = null;
      }
      if (onAudioLevels) {
        onAudioLevels({ 
          left: 0, 
          right: 0, 
          leftRms: 0, 
          rightRms: 0, 
          rms: 0, 
          lufs: -70,
          leftLufs: -70,
          rightLufs: -70
        });
      }
    }

    return () => {
      if (animationFrame.current) {
        cancelAnimationFrame(animationFrame.current);
        animationFrame.current = null;
      }
    };
  }, [isPlaying, onAudioLevels, crossfadeVolume]);

  // Notify parent of play state changes
  useEffect(() => {
    if (onPlayStateChange) {
      onPlayStateChange(isPlaying);
    }
  }, [isPlaying, onPlayStateChange]);

  // Control functions
  const togglePlayPause = useCallback(async () => {
    const audio = audioRef.current;
    if (!audio || !canPlay) {
      console.log('Cannot play: audio not ready');
      return;
    }

    try {
      if (isPlaying) {
        console.log('Pausing audio');
        audio.pause();
      } else {
        console.log('Starting playback');
        
        // Ensure audio context is ready
        await setupAudioContext();
        
        if (audioContext.current && audioContext.current.state === 'suspended') {
          await audioContext.current.resume();
        }
        
        await audio.play();
        console.log('Playback started successfully');
      }
    } catch (error) {
      console.error('Playback error:', error);
      setError(`Playback failed: ${error.message}`);
    }
  }, [isPlaying, canPlay, setupAudioContext]);

  const handleVolumeChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const newVolume = parseFloat(e.target.value);
    setVolume(newVolume);
    setIsMuted(newVolume === 0);
  }, []);

  const toggleMute = useCallback(() => {
    setIsMuted(!isMuted);
  }, [isMuted]);

  const handleWaveformClick = useCallback((e: React.MouseEvent<HTMLCanvasElement>) => {
    const audio = audioRef.current;
    if (!audio || !duration || !canPlay) return;

    const canvas = e.currentTarget;
    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const clickTime = (x / rect.width) * duration;
    
    audio.currentTime = clickTime;
    console.log('Seeked to:', clickTime);
  }, [duration, canPlay]);

  const formatTime = (time: number) => {
    if (!isFinite(time) || isNaN(time)) return '0:00';
    const minutes = Math.floor(time / 60);
    const seconds = Math.floor(time % 60);
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  const formatSampleRate = (sampleRate: number) => {
    if (sampleRate >= 1000) {
      return `${(sampleRate / 1000).toFixed(1)}kHz`;
    }
    return `${sampleRate}Hz`;
  };

  const getChannelText = (channels: number) => {
    switch (channels) {
      case 1: return 'Mono';
      case 2: return 'Stereo';
      case 6: return '5.1';
      case 8: return '7.1';
      default: return `${channels}ch`;
    }
  };

  if (error) {
    return (
      <div className="glass-panel rounded-3xl p-6 border border-slate-600">
        <div className="flex flex-col items-center justify-center h-64 gap-4">
          <div className="text-red-400 text-sm text-center">{error}</div>
          <button
            onClick={() => {
              setError(null);
              setIsLoading(true);
              currentFile.current = null; // Force re-initialization
            }}
            className="px-4 py-2 bg-red-500 hover:bg-red-600 text-white rounded-xl text-sm transition-colors"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className={`glass-panel rounded-3xl p-6 border border-slate-600 transition-all duration-300 ${
      crossfadeVolume === 0 ? 'opacity-60' : ''
    }`}>
      <audio ref={audioRef} preload="auto" />
      
      {/* Header - Shows filename and time */}
      <div className="flex items-center justify-between mb-6">
        <h3 className={`text-base font-semibold ${config.textColor} ${crossfadeVolume === 0 ? 'opacity-50' : ''} truncate`}>
          {file.name}
        </h3>
        <div className="text-xs text-audio-text-dim font-mono">
          {formatTime(currentTime)} / {formatTime(duration)}
        </div>
      </div>

      {/* Stereo Waveforms */}
      <div className="mb-6 space-y-2">
        {isLoading ? (
          <div className="w-full h-32 bg-audio-bg rounded-2xl border border-slate-700/50 flex items-center justify-center">
            <div className="flex items-center gap-2 text-audio-text-dim">
              <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin"></div>
              <span className="text-sm">Loading stereo waveforms...</span>
            </div>
          </div>
        ) : (
          <>
            {/* Left Channel Waveform */}
            <canvas
              ref={leftCanvasRef}
              width={800}
              height={60}
              className="w-full h-16 bg-audio-bg rounded-t-2xl border border-b-0 border-slate-700/50 cursor-pointer hover:border-slate-600 transition-colors"
              onClick={handleWaveformClick}
            />
            {/* Right Channel Waveform */}
            <canvas
              ref={rightCanvasRef}
              width={800}
              height={60}
              className="w-full h-16 bg-audio-bg rounded-b-2xl border border-t-0 border-slate-700/50 cursor-pointer hover:border-slate-600 transition-colors"
              onClick={handleWaveformClick}
            />
          </>
        )}
      </div>

      {/* Controls with Inline Metadata */}
      <div className="flex items-center justify-between">
        {/* Play Button */}
        <div className="flex items-center">
          <button
            onClick={togglePlayPause}
            className={`p-3 rounded-2xl transition-all duration-200 ${config.bgColor} ${config.hoverColor} text-white shadow-lg neon-glow-fusion disabled:opacity-50 disabled:cursor-not-allowed ${
              crossfadeVolume === 0 ? 'opacity-60' : ''
            }`}
            disabled={!canPlay}
          >
            {isPlaying ? <Pause size={20} /> : <Play size={20} />}
          </button>
        </div>

        {/* Metadata in Center */}
        <div className="flex items-center gap-6 text-xs text-audio-text-dim font-mono">
          {/* File Size */}
          <div className="text-center">
            <div className="text-white font-semibold">{(file.size / 1024 / 1024).toFixed(2)} MB</div>
            <div className="text-audio-text-dim">Size</div>
          </div>
          
          {/* Sample Rate */}
          <div className="text-center">
            <div className="text-white font-semibold">
              {audioMetadata ? formatSampleRate(audioMetadata.sampleRate) : '---'}
            </div>
            <div className="text-audio-text-dim">Sample Rate</div>
          </div>
          
          {/* Bit Depth / Bitrate */}
          <div className="text-center">
            <div className="text-white font-semibold">
              {audioMetadata ? audioMetadata.bitDepth : '---'}
            </div>
            <div className="text-audio-text-dim">
              {audioMetadata?.format === 'MP3' || audioMetadata?.format === 'AAC' ? 'Bitrate' : 'Bit Depth'}
            </div>
          </div>
          
          {/* Channels */}
          <div className="text-center">
            <div className="text-white font-semibold">
              {audioMetadata ? getChannelText(audioMetadata.channels) : '---'}
            </div>
            <div className="text-audio-text-dim">Channels</div>
          </div>
        </div>

        {/* Volume Control */}
        <div className="flex items-center gap-3">
          <button
            onClick={toggleMute}
            className="p-2 glass-panel rounded-xl hover:bg-gradient-to-br hover:from-emerald-500/20 hover:to-purple-500/20 transition-all duration-200 text-audio-text-dim hover:text-white border border-slate-600 hover:border-transparent"
          >
            {isMuted || volume === 0 ? <VolumeX size={16} /> : <Volume2 size={16} />}
          </button>
          
          <div className="flex items-center gap-2">
            <input
              type="range"
              min="0"
              max="1"
              step="0.01"
              value={isMuted ? 0 : volume}
              onChange={handleVolumeChange}
              className="w-20 h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer slider"
              style={{
                background: `linear-gradient(to right, ${config.waveColor} 0%, ${config.waveColor} ${(isMuted ? 0 : volume) * 100}%, #334155 ${(isMuted ? 0 : volume) * 100}%, #334155 100%)`
              }}
            />
            <span className="text-xs text-audio-text-dim font-mono w-8">
              {Math.round((isMuted ? 0 : volume) * 100)}%
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}
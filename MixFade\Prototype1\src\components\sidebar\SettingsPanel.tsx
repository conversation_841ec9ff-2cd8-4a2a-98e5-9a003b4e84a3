import React from 'react';
import { Monitor, Volume2, Keyboard, Palette, Download, Info } from 'lucide-react';

export function SettingsPanel() {
  return (
    <div className="p-4 space-y-6">
      {/* Audio Settings */}
      <div className="space-y-2">
        <h3 className="text-xs font-semibold text-slate-400 uppercase tracking-wide">Audio</h3>
        <div className="space-y-3">
          <div className="space-y-2">
            <label className="text-sm text-slate-300">Audio Device</label>
            <select className="w-full px-3 py-2 bg-slate-800 border border-slate-700 rounded-md text-sm text-slate-300 focus:outline-none focus:border-emerald-500">
              <option>Default Audio Device</option>
              <option>Speakers (Realtek Audio)</option>
              <option>Headphones (USB)</option>
            </select>
          </div>
          
          <div className="space-y-2">
            <label className="text-sm text-slate-300">Sample Rate</label>
            <select className="w-full px-3 py-2 bg-slate-800 border border-slate-700 rounded-md text-sm text-slate-300 focus:outline-none focus:border-emerald-500">
              <option>44.1 kHz</option>
              <option>48 kHz</option>
              <option>96 kHz</option>
            </select>
          </div>

          <div className="space-y-2">
            <label className="text-sm text-slate-300">Buffer Size</label>
            <select className="w-full px-3 py-2 bg-slate-800 border border-slate-700 rounded-md text-sm text-slate-300 focus:outline-none focus:border-emerald-500">
              <option>128 samples</option>
              <option>256 samples</option>
              <option>512 samples</option>
              <option>1024 samples</option>
            </select>
          </div>
        </div>
      </div>

      {/* Appearance */}
      <div className="space-y-2">
        <h3 className="text-xs font-semibold text-slate-400 uppercase tracking-wide">Appearance</h3>
        <div className="space-y-3">
          <div className="space-y-2">
            <label className="text-sm text-slate-300">Theme</label>
            <select className="w-full px-3 py-2 bg-slate-800 border border-slate-700 rounded-md text-sm text-slate-300 focus:outline-none focus:border-emerald-500">
              <option>Dark</option>
              <option>Light</option>
              <option>Auto</option>
            </select>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-slate-300">Show Waveform</span>
            <button className="w-8 h-4 bg-emerald-500 rounded-full">
              <div className="w-3 h-3 bg-white rounded-full translate-x-4" />
            </button>
          </div>

          <div className="flex items-center justify-between">
            <span className="text-sm text-slate-300">Show Level Meters</span>
            <button className="w-8 h-4 bg-emerald-500 rounded-full">
              <div className="w-3 h-3 bg-white rounded-full translate-x-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Keyboard Shortcuts */}
      <div className="space-y-2">
        <h3 className="text-xs font-semibold text-slate-400 uppercase tracking-wide">Shortcuts</h3>
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-slate-300">Play/Pause</span>
            <span className="text-slate-500 font-mono">Space</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-slate-300">Crossfade A+B</span>
            <span className="text-slate-500 font-mono">Tab</span>
          </div>
          <div className="flex items-center justify-between text-sm">
            <span className="text-slate-300">Toggle Sidebar</span>
            <span className="text-slate-500 font-mono">Ctrl+B</span>
          </div>
          <button className="w-full mt-2 px-3 py-2 text-sm text-slate-300 bg-slate-800 hover:bg-slate-700 rounded-md transition-colors">
            Customize Shortcuts
          </button>
        </div>
      </div>

      {/* Quick Settings */}
      <div className="space-y-2">
        <h3 className="text-xs font-semibold text-slate-400 uppercase tracking-wide">Quick Settings</h3>
        <div className="space-y-1">
          <button className="w-full flex items-center space-x-3 px-3 py-2 text-sm text-slate-300 hover:bg-slate-800 rounded-md transition-colors">
            <Monitor size={16} className="text-blue-500" />
            <span>Display Settings</span>
          </button>
          <button className="w-full flex items-center space-x-3 px-3 py-2 text-sm text-slate-300 hover:bg-slate-800 rounded-md transition-colors">
            <Volume2 size={16} className="text-emerald-500" />
            <span>Audio Preferences</span>
          </button>
          <button className="w-full flex items-center space-x-3 px-3 py-2 text-sm text-slate-300 hover:bg-slate-800 rounded-md transition-colors">
            <Keyboard size={16} className="text-purple-500" />
            <span>Keyboard Settings</span>
          </button>
          <button className="w-full flex items-center space-x-3 px-3 py-2 text-sm text-slate-300 hover:bg-slate-800 rounded-md transition-colors">
            <Download size={16} className="text-yellow-500" />
            <span>Check for Updates</span>
          </button>
        </div>
      </div>
    </div>
  );
}

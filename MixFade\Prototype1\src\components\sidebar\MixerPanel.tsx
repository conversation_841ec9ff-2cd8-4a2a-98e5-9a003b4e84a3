import React from 'react';
import { Volume2, VolumeX, Sliders, RotateCcw, Play, Pause } from 'lucide-react';

export function MixerPanel() {
  return (
    <div className="p-4 space-y-6">
      {/* Master Controls */}
      <div className="space-y-2">
        <h3 className="text-xs font-semibold text-slate-400 uppercase tracking-wide">Master Controls</h3>
        <div className="space-y-3">
          {/* Master Volume */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm text-slate-300">Master Volume</label>
              <span className="text-xs text-slate-500">85%</span>
            </div>
            <div className="flex items-center space-x-2">
              <VolumeX size={14} className="text-slate-500" />
              <input
                type="range"
                min="0"
                max="100"
                defaultValue="85"
                className="flex-1 h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer slider"
              />
              <Volume2 size={14} className="text-slate-400" />
            </div>
          </div>

          {/* Crossfade */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm text-slate-300">Crossfade</label>
              <span className="text-xs text-slate-500">Center</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-xs text-slate-500">A</span>
              <input
                type="range"
                min="0"
                max="100"
                defaultValue="50"
                className="flex-1 h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer slider"
              />
              <span className="text-xs text-slate-500">B</span>
            </div>
          </div>
        </div>
      </div>

      {/* Track A Controls */}
      <div className="space-y-2">
        <h3 className="text-xs font-semibold text-emerald-400 uppercase tracking-wide">Track A</h3>
        <div className="space-y-3 p-3 bg-slate-800 rounded-lg border border-emerald-500/20">
          {/* Play/Pause */}
          <div className="flex items-center justify-between">
            <span className="text-sm text-slate-300">Playback</span>
            <button className="p-2 bg-emerald-600 hover:bg-emerald-700 rounded-md transition-colors">
              <Play size={14} className="text-white" />
            </button>
          </div>

          {/* Volume */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm text-slate-300">Volume</label>
              <span className="text-xs text-slate-500">75%</span>
            </div>
            <input
              type="range"
              min="0"
              max="100"
              defaultValue="75"
              className="w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer slider"
            />
          </div>

          {/* EQ Controls */}
          <div className="space-y-2">
            <label className="text-sm text-slate-300">3-Band EQ</label>
            <div className="grid grid-cols-3 gap-2">
              <div className="text-center">
                <label className="text-xs text-slate-500">High</label>
                <input
                  type="range"
                  min="-12"
                  max="12"
                  defaultValue="0"
                  orient="vertical"
                  className="w-full h-16 bg-slate-700 rounded-lg appearance-none cursor-pointer slider vertical"
                />
              </div>
              <div className="text-center">
                <label className="text-xs text-slate-500">Mid</label>
                <input
                  type="range"
                  min="-12"
                  max="12"
                  defaultValue="0"
                  orient="vertical"
                  className="w-full h-16 bg-slate-700 rounded-lg appearance-none cursor-pointer slider vertical"
                />
              </div>
              <div className="text-center">
                <label className="text-xs text-slate-500">Low</label>
                <input
                  type="range"
                  min="-12"
                  max="12"
                  defaultValue="0"
                  orient="vertical"
                  className="w-full h-16 bg-slate-700 rounded-lg appearance-none cursor-pointer slider vertical"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Track B Controls */}
      <div className="space-y-2">
        <h3 className="text-xs font-semibold text-purple-400 uppercase tracking-wide">Track B</h3>
        <div className="space-y-3 p-3 bg-slate-800 rounded-lg border border-purple-500/20">
          {/* Play/Pause */}
          <div className="flex items-center justify-between">
            <span className="text-sm text-slate-300">Playback</span>
            <button className="p-2 bg-purple-600 hover:bg-purple-700 rounded-md transition-colors">
              <Pause size={14} className="text-white" />
            </button>
          </div>

          {/* Volume */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm text-slate-300">Volume</label>
              <span className="text-xs text-slate-500">60%</span>
            </div>
            <input
              type="range"
              min="0"
              max="100"
              defaultValue="60"
              className="w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer slider"
            />
          </div>

          {/* EQ Controls */}
          <div className="space-y-2">
            <label className="text-sm text-slate-300">3-Band EQ</label>
            <div className="grid grid-cols-3 gap-2">
              <div className="text-center">
                <label className="text-xs text-slate-500">High</label>
                <input
                  type="range"
                  min="-12"
                  max="12"
                  defaultValue="2"
                  orient="vertical"
                  className="w-full h-16 bg-slate-700 rounded-lg appearance-none cursor-pointer slider vertical"
                />
              </div>
              <div className="text-center">
                <label className="text-xs text-slate-500">Mid</label>
                <input
                  type="range"
                  min="-12"
                  max="12"
                  defaultValue="-1"
                  orient="vertical"
                  className="w-full h-16 bg-slate-700 rounded-lg appearance-none cursor-pointer slider vertical"
                />
              </div>
              <div className="text-center">
                <label className="text-xs text-slate-500">Low</label>
                <input
                  type="range"
                  min="-12"
                  max="12"
                  defaultValue="3"
                  orient="vertical"
                  className="w-full h-16 bg-slate-700 rounded-lg appearance-none cursor-pointer slider vertical"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="space-y-2">
        <h3 className="text-xs font-semibold text-slate-400 uppercase tracking-wide">Quick Actions</h3>
        <div className="grid grid-cols-2 gap-2">
          <button className="flex items-center justify-center space-x-2 px-3 py-2 text-sm text-slate-300 bg-slate-800 hover:bg-slate-700 rounded-md transition-colors">
            <RotateCcw size={14} />
            <span>Reset</span>
          </button>
          <button className="flex items-center justify-center space-x-2 px-3 py-2 text-sm text-slate-300 bg-slate-800 hover:bg-slate-700 rounded-md transition-colors">
            <Sliders size={14} />
            <span>Auto</span>
          </button>
        </div>
      </div>
    </div>
  );
}

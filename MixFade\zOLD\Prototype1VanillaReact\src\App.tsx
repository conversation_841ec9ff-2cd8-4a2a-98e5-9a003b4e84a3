import React, { useState, useCallback, useRef } from 'react';
import { Activity } from 'lucide-react';
import { FileUpload } from './components/FileUpload';
import { ABSwitch } from './components/ABSwitch';
import { WaveformPlayer } from './components/WaveformPlayer';
import { LevelMeter } from './components/LevelMeter';
import Header from './components/Header';

function App() {
  const [trackA, setTrackA] = useState<File | null>(null);
  const [trackB, setTrackB] = useState<File | null>(null);
  const [activeTrack, setActiveTrack] = useState<'A' | 'B' | 'both'>('A');
  const [isTrackAPlaying, setIsTrackAPlaying] = useState(false);
  const [isTrackBPlaying, setIsTrackBPlaying] = useState(false);
  const [trackAAudioLevels, setTrackAAudioLevels] = useState({ left: 0, right: 0, leftRms: 0, rightRms: 0, rms: 0, lufs: -70 });
  const [trackBAudioLevels, setTrackBAudioLevels] = useState({ left: 0, right: 0, leftRms: 0, rightRms: 0, rms: 0, lufs: -70 });
  
  // Crossfade state
  const [volumeA, setVolumeA] = useState(1);
  const [volumeB, setVolumeB] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const transitionRef = useRef<NodeJS.Timeout | null>(null);

  const hasAnyAudio = trackA || trackB;
  const hasBothAudio = trackA && trackB;

  // Equal Power Crossfade Function
  const equalPowerCrossfade = useCallback((progress: number) => {
    // Equal power crossfade using sine/cosine curves
    // This maintains constant perceived loudness during transition
    const angle = progress * (Math.PI / 2); // 0 to π/2
    return {
      fadeOut: Math.cos(angle), // Smooth curve from 1 to 0
      fadeIn: Math.sin(angle)   // Smooth curve from 0 to 1
    };
  }, []);

  // DJ-style crossfade transition with equal power curve
  const performCrossfade = useCallback((fromTrack: 'A' | 'B', toTrack: 'A' | 'B') => {
    if (isTransitioning) return; // Prevent multiple transitions
    
    setIsTransitioning(true);
    const transitionDuration = 2500; // 2.5 seconds
    const steps = 60; // Higher resolution for smoother curve
    const stepDuration = transitionDuration / steps;
    
    let currentStep = 0;
    
    const transition = () => {
      currentStep++;
      const linearProgress = currentStep / steps;
      
      // Apply equal power crossfade curve
      const { fadeOut, fadeIn } = equalPowerCrossfade(linearProgress);
      
      if (fromTrack === 'A' && toTrack === 'B') {
        setVolumeA(fadeOut);
        setVolumeB(fadeIn);
      } else if (fromTrack === 'B' && toTrack === 'A') {
        setVolumeA(fadeIn);
        setVolumeB(fadeOut);
      }
      
      if (currentStep < steps) {
        transitionRef.current = setTimeout(transition, stepDuration);
      } else {
        setIsTransitioning(false);
        // Set final state with precise values
        if (toTrack === 'A') {
          setVolumeA(1);
          setVolumeB(0);
          setActiveTrack('A');
        } else {
          setVolumeA(0);
          setVolumeB(1);
          setActiveTrack('B');
        }
      }
    };
    
    transition();
  }, [isTransitioning, equalPowerCrossfade]);

  // Handle track switching with crossfade logic
  const handleTrackSwitch = useCallback((track: 'A' | 'B' | 'both') => {
    // Clear any existing transition
    if (transitionRef.current) {
      clearTimeout(transitionRef.current);
      transitionRef.current = null;
      setIsTransitioning(false);
    }

    if (track === 'both') {
      // Crossfade from current active track to the other
      if (activeTrack === 'A') {
        performCrossfade('A', 'B');
      } else if (activeTrack === 'B') {
        performCrossfade('B', 'A');
      } else {
        // If already in transition or both, default to A
        setVolumeA(1);
        setVolumeB(0);
        setActiveTrack('A');
      }
    } else {
      // Direct selection - immediate switch
      setActiveTrack(track);
      if (track === 'A') {
        setVolumeA(1);
        setVolumeB(0);
      } else {
        setVolumeA(0);
        setVolumeB(1);
      }
    }
  }, [activeTrack, performCrossfade]);

  // Cleanup transition on unmount
  React.useEffect(() => {
    return () => {
      if (transitionRef.current) {
        clearTimeout(transitionRef.current);
      }
    };
  }, []);

  return (
    <div className="min-h-screen text-white" style={{
      background: `linear-gradient(rgba(15, 23, 42, 0.6), rgba(15, 23, 42, 0.6)), linear-gradient(to right, rgb(16, 185, 129), rgb(168, 85, 247))`
    }}>
      <Header />

      <div className="p-6 space-y-8">
        {/* File Upload Section with Crossfade Control */}
        <section className={`${!hasAnyAudio ? 'min-h-[60vh] flex items-center justify-center' : ''}`}>
          <div className={`${!hasAnyAudio ? 'w-full max-w-6xl' : ''}`}>
            {/* Single Row: Upload A - Crossfade - Upload B */}
            <div className="grid grid-cols-1 lg:grid-cols-5 gap-6 items-center">
              {/* Audio A Upload - 2 columns */}
              <div className="lg:col-span-2">
                <FileUpload
                  label="Audio A"
                  color="green"
                  file={trackA}
                  onFileSelect={setTrackA}
                />
              </div>
              
              {/* Crossfade Control - 1 column, centered */}
              <div className="lg:col-span-1 flex justify-center">
                {hasBothAudio ? (
                  <ABSwitch 
                    activeTrack={activeTrack} 
                    onSwitch={handleTrackSwitch}
                    isTransitioning={isTransitioning}
                    volumeA={volumeA}
                    volumeB={volumeB}
                  />
                ) : (
                  <div className="w-full h-32 flex items-center justify-center">
                    <div className="text-audio-text-dim text-sm text-center">
                      <div className="w-16 h-16 rounded-2xl border-2 border-gradient-to-r from-emerald-500/20 to-purple-500/20 flex items-center justify-center mb-2 mx-auto">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" stopColor="rgb(16, 185, 129)" />
                                    <stop offset="100%" stopColor="rgb(168, 85, 247)" />
                                </linearGradient>
                            </defs>
                            <Activity size={24} stroke="url(#iconGradient)" />
                        </svg>
                      </div>
                      <p>Upload both files</p>
                      <p className="text-xs">to enable crossfade</p>
                    </div>
                  </div>
                )}
              </div>
              
              {/* Audio B Upload - 2 columns */}
              <div className="lg:col-span-2">
                <FileUpload
                  label="Audio B"
                  color="purple"
                  file={trackB}
                  onFileSelect={setTrackB}
                />
              </div>
            </div>
          </div>
        </section>

        {/* Only show analysis sections when audio is uploaded */}
        {hasAnyAudio && (
          <>
            {/* Waveform Players */}
            <section className="space-y-6">
              <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
                {trackA && (
                  <WaveformPlayer
                    file={trackA}
                    color="green"
                    label="Audio A"
                    onPlayStateChange={setIsTrackAPlaying}
                    onAudioLevels={setTrackAAudioLevels}
                    crossfadeVolume={volumeA}
                  />
                )}
                {trackB && (
                  <WaveformPlayer
                    file={trackB}
                    color="purple"
                    label="Audio B"
                    onPlayStateChange={setIsTrackBPlaying}
                    onAudioLevels={setTrackBAudioLevels}
                    crossfadeVolume={volumeB}
                  />
                )}
              </div>
            </section>

            {/* Level Meters */}
            <section className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {trackA && (
                  <LevelMeter 
                    label="Audio A Levels" 
                    color="green" 
                    isActive={true} 
                    isPlaying={isTrackAPlaying}
                    audioLevels={trackAAudioLevels}
                    crossfadeVolume={volumeA}
                  />
                )}
                {trackB && (
                  <LevelMeter 
                    label="Audio B Levels" 
                    color="purple" 
                    isActive={true} 
                    isPlaying={isTrackBPlaying}
                    audioLevels={trackBAudioLevels}
                    crossfadeVolume={volumeB}
                  />
                )}
              </div>
            </section>
          </>
        )}
      </div>
    </div>
  );
}

export default App;
interface HeaderProps {
  isSidebarCollapsed?: boolean;
}

const Header = ({ isSidebarCollapsed = false }: HeaderProps) => {
  console.log('Header - isSidebarCollapsed:', isSidebarCollapsed); // Debug log

  return (
    <header
      className="sticky top-0 z-50 transition-all duration-300"
      style={{
        backgroundColor: isSidebarCollapsed ? 'red' : 'blue', // Obvious colors for debugging
        backdropFilter: isSidebarCollapsed ? 'none' : 'blur(4px)',
        borderBottom: isSidebarCollapsed
          ? '3px solid yellow'
          : '3px solid green',
        minHeight: '60px' // Make sure it's visible
      }}>
      <div className="flex items-center justify-center p-4">
        {/* Centered Logo */}
        <img src="/mixfade_logo.png" alt="MixFade Logo" className="h-10" />
      </div>
    </header>
  );
};

export default Header;

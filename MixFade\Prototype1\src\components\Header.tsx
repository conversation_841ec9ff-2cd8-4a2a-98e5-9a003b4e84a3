interface HeaderProps {
  isSidebarCollapsed?: boolean;
}

const Header = ({ isSidebarCollapsed = false }: HeaderProps) => {
  console.log('Header - isSidebarCollapsed:', isSidebarCollapsed); // Debug log

  return (
    <header
      className="sticky top-0 z-50 transition-all duration-300"
      style={{
        backgroundColor: isSidebarCollapsed ? 'transparent' : 'rgba(15, 23, 42, 0.8)',
        backdropFilter: isSidebarCollapsed ? 'none' : 'blur(4px)',
        borderBottom: isSidebarCollapsed
          ? '1px solid rgba(255, 255, 255, 0.05)'
          : '1px solid rgba(71, 85, 105, 0.5)'
      }}>
      <div className="flex items-center justify-center p-4">
        {/* Centered Logo */}
        <img src="/mixfade_logo.png" alt="MixFade Logo" className="h-10" />
      </div>
    </header>
  );
};

export default Header;

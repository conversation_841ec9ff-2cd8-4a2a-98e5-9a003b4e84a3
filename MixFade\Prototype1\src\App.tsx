import React, { useState, useCallback, useRef, useEffect } from 'react';
import { Activity } from 'lucide-react';
import { FileUpload } from './components/FileUpload';
import { ABSwitch } from './components/ABSwitch';
import { WaveformPlayer, WaveformPlayerRef } from './components/WaveformPlayer';
import { LevelMeter } from './components/LevelMeter';
import { ActivityBar } from './components/ActivityBar';
import { Sidebar } from './components/Sidebar';
import { useKeyboardShortcuts } from './hooks/useKeyboardShortcuts';
import Header from './components/Header';

// Recent files interface
interface RecentFile {
  id: string;
  name: string;
  size: string;
  lastModified: string;
  lastUsedSide: 'A' | 'B';
  file?: File; // Keep File object in memory during session
  filePath?: string; // For future file system access
}

function App() {
  const [trackA, setTrackA] = useState<File | null>(null);
  const [trackB, setTrackB] = useState<File | null>(null);
  const [activeTrack, setActiveTrack] = useState<'A' | 'B' | 'both'>('A');
  const [isTrackAPlaying, setIsTrackAPlaying] = useState(false);
  const [isTrackBPlaying, setIsTrackBPlaying] = useState(false);
  const [trackAAudioLevels, setTrackAAudioLevels] = useState({ left: 0, right: 0, leftRms: 0, rightRms: 0, rms: 0, lufs: -70 });
  const [trackBAudioLevels, setTrackBAudioLevels] = useState({ left: 0, right: 0, leftRms: 0, rightRms: 0, rms: 0, lufs: -70 });

  // Recent files state
  const [recentFiles, setRecentFiles] = useState<RecentFile[]>([]);
  
  // Crossfade state
  const [volumeA, setVolumeA] = useState(1);
  const [volumeB, setVolumeB] = useState(0);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const transitionRef = useRef<NodeJS.Timeout | null>(null);

  // Refs for controlling waveform players
  const waveformPlayerARef = useRef<WaveformPlayerRef>(null);
  const waveformPlayerBRef = useRef<WaveformPlayerRef>(null);

  // Navigation state
  const [activeActivity, setActiveActivity] = useState('files');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);

  const hasAnyAudio = trackA || trackB;
  const hasBothAudio = trackA && trackB;

  // Load recent files from localStorage on mount
  useEffect(() => {
    const savedRecentFiles = localStorage.getItem('mixfade-recent-files');
    if (savedRecentFiles) {
      try {
        setRecentFiles(JSON.parse(savedRecentFiles));
      } catch (error) {
        console.warn('Failed to load recent files from localStorage:', error);
      }
    }
  }, []);

  // Save recent files to localStorage whenever it changes (without File objects)
  useEffect(() => {
    const filesToSave = recentFiles.map(({ file, ...rest }) => rest);
    localStorage.setItem('mixfade-recent-files', JSON.stringify(filesToSave));
  }, [recentFiles]);

  // Add file to recent files
  const addToRecentFiles = useCallback((file: File, side: 'A' | 'B') => {
    const fileId = `${file.name}-${file.size}-${file.lastModified}`;

    setRecentFiles(prev => {
      // Remove existing entry if it exists
      const filtered = prev.filter(f => f.id !== fileId);

      // Add new entry at the beginning
      const newFile: RecentFile = {
        id: fileId,
        name: file.name,
        size: `${(file.size / (1024 * 1024)).toFixed(1)} MB`,
        lastModified: new Date(file.lastModified).toLocaleDateString(),
        lastUsedSide: side,
        file: file, // Store the actual File object in memory
      };

      // Keep only the last 10 files
      return [newFile, ...filtered].slice(0, 10);
    });
  }, []);

  // Enhanced track setters that also update recent files
  const setTrackAWithRecent = useCallback((file: File | null) => {
    setTrackA(file);
    if (file) {
      addToRecentFiles(file, 'A');
    }
  }, [addToRecentFiles]);

  const setTrackBWithRecent = useCallback((file: File | null) => {
    setTrackB(file);
    if (file) {
      addToRecentFiles(file, 'B');
    }
  }, [addToRecentFiles]);

  // Load file from recent files
  const loadFileFromRecent = useCallback((recentFile: RecentFile) => {
    // If we have the File object in memory, use it directly
    if (recentFile.file) {
      if (recentFile.lastUsedSide === 'A') {
        setTrackAWithRecent(recentFile.file);
      } else {
        setTrackBWithRecent(recentFile.file);
      }
      return;
    }

    // Fallback: ask user to select the file again (for files loaded from localStorage)
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'audio/*';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file && file.name === recentFile.name) {
        if (recentFile.lastUsedSide === 'A') {
          setTrackAWithRecent(file);
        } else {
          setTrackBWithRecent(file);
        }
      }
    };
    input.click();
  }, [setTrackAWithRecent, setTrackBWithRecent]);

  // Keyboard shortcut functions
  const handlePlayPause = useCallback(() => {
    if (activeTrack === 'A' && waveformPlayerARef.current) {
      waveformPlayerARef.current.togglePlayPause();
    } else if (activeTrack === 'B' && waveformPlayerBRef.current) {
      waveformPlayerBRef.current.togglePlayPause();
    } else if (activeTrack === 'both') {
      // If both are active, toggle both
      if (waveformPlayerARef.current) waveformPlayerARef.current.togglePlayPause();
      if (waveformPlayerBRef.current) waveformPlayerBRef.current.togglePlayPause();
    }
  }, [activeTrack]);

  // Equal Power Crossfade Function
  const equalPowerCrossfade = useCallback((progress: number) => {
    // Equal power crossfade using sine/cosine curves
    // This maintains constant perceived loudness during transition
    const angle = progress * (Math.PI / 2); // 0 to π/2
    return {
      fadeOut: Math.cos(angle), // Smooth curve from 1 to 0
      fadeIn: Math.sin(angle)   // Smooth curve from 0 to 1
    };
  }, []);

  // DJ-style crossfade transition with equal power curve
  const performCrossfade = useCallback((fromTrack: 'A' | 'B', toTrack: 'A' | 'B') => {
    if (isTransitioning) return; // Prevent multiple transitions
    
    setIsTransitioning(true);
    const transitionDuration = 2500; // 2.5 seconds
    const steps = 60; // Higher resolution for smoother curve
    const stepDuration = transitionDuration / steps;
    
    let currentStep = 0;
    
    const transition = () => {
      currentStep++;
      const linearProgress = currentStep / steps;
      
      // Apply equal power crossfade curve
      const { fadeOut, fadeIn } = equalPowerCrossfade(linearProgress);
      
      if (fromTrack === 'A' && toTrack === 'B') {
        setVolumeA(fadeOut);
        setVolumeB(fadeIn);
      } else if (fromTrack === 'B' && toTrack === 'A') {
        setVolumeA(fadeIn);
        setVolumeB(fadeOut);
      }
      
      if (currentStep < steps) {
        transitionRef.current = setTimeout(transition, stepDuration);
      } else {
        setIsTransitioning(false);
        // Set final state with precise values
        if (toTrack === 'A') {
          setVolumeA(1);
          setVolumeB(0);
          setActiveTrack('A');
        } else {
          setVolumeA(0);
          setVolumeB(1);
          setActiveTrack('B');
        }
      }
    };
    
    transition();
  }, [isTransitioning, equalPowerCrossfade]);

  // Handle track switching with crossfade logic
  const handleTrackSwitch = useCallback((track: 'A' | 'B' | 'both') => {
    // Clear any existing transition
    if (transitionRef.current) {
      clearTimeout(transitionRef.current);
      transitionRef.current = null;
      setIsTransitioning(false);
    }

    if (track === 'both') {
      // Crossfade from current active track to the other
      if (activeTrack === 'A') {
        performCrossfade('A', 'B');
      } else if (activeTrack === 'B') {
        performCrossfade('B', 'A');
      } else {
        // If already in transition or both, default to A
        setVolumeA(1);
        setVolumeB(0);
        setActiveTrack('A');
      }
    } else {
      // Direct selection - immediate switch
      setActiveTrack(track);
      if (track === 'A') {
        setVolumeA(1);
        setVolumeB(0);
      } else {
        setVolumeA(0);
        setVolumeB(1);
      }
    }
  }, [activeTrack, performCrossfade]);

  // Set up keyboard shortcuts (after all functions are defined)
  useKeyboardShortcuts({
    'space': handlePlayPause,
    'tab': () => {
      if (hasBothAudio) {
        handleTrackSwitch('both');
      }
    },
    // Navigation shortcuts
    'ctrl+b': () => setSidebarCollapsed(!sidebarCollapsed),
    'ctrl+shift+e': () => setActiveActivity('files'),
    'ctrl+shift+a': () => setActiveActivity('analysis'),
    'ctrl+,': () => setActiveActivity('settings'),
    'f1': () => setActiveActivity('help'),
  });

  // Cleanup transition on unmount
  React.useEffect(() => {
    return () => {
      if (transitionRef.current) {
        clearTimeout(transitionRef.current);
      }
    };
  }, []);

  return (
    <div className="min-h-screen text-white flex">
      {/* Activity Bar */}
      <ActivityBar
        activeId={activeActivity}
        onActivityChange={setActiveActivity}
        isSidebarCollapsed={sidebarCollapsed}
        onToggleSidebar={() => setSidebarCollapsed(!sidebarCollapsed)}
      />

      {/* Sidebar */}
      <Sidebar
        activeActivity={activeActivity}
        isCollapsed={sidebarCollapsed}
        onToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
        recentFiles={recentFiles}
        onLoadFileFromRecent={loadFileFromRecent}
        onLoadToA={setTrackAWithRecent}
        onLoadToB={setTrackBWithRecent}
      />



      {/* Main Content Area */}
      <div className="flex-1 flex flex-col min-h-screen">
        {/* Header */}
        <Header isSidebarCollapsed={sidebarCollapsed} />

        {/* Main Content */}
        <div className="flex-1 p-6 space-y-8" style={{
          background: `linear-gradient(rgba(15, 23, 42, 0.6), rgba(15, 23, 42, 0.6)), linear-gradient(to right, rgb(16, 185, 129), rgb(168, 85, 247))`
        }}>
        {/* File Upload Section with Crossfade Control */}
        <section className={`${!hasAnyAudio ? 'min-h-[60vh] flex items-center justify-center' : ''}`}>
          <div className={`${!hasAnyAudio ? 'w-full max-w-6xl' : ''}`}>
            {/* Single Row: Upload A - Crossfade - Upload B */}
            <div className="grid grid-cols-1 lg:grid-cols-5 gap-6 items-center">
              {/* Audio A Upload - 2 columns */}
              <div className="lg:col-span-2">
                <FileUpload
                  label="Audio A"
                  color="green"
                  file={trackA}
                  onFileSelect={setTrackAWithRecent}
                />
              </div>
              
              {/* Crossfade Control - 1 column, centered */}
              <div className="lg:col-span-1 flex justify-center">
                {hasBothAudio ? (
                  <ABSwitch 
                    activeTrack={activeTrack} 
                    onSwitch={handleTrackSwitch}
                    isTransitioning={isTransitioning}
                    volumeA={volumeA}
                    volumeB={volumeB}
                  />
                ) : (
                  <div className="w-full h-32 flex items-center justify-center">
                    <div className="text-audio-text-dim text-sm text-center">
                      <div className="w-16 h-16 rounded-2xl border-2 border-gradient-to-r from-emerald-500/20 to-purple-500/20 flex items-center justify-center mb-2 mx-auto">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <linearGradient id="iconGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" stopColor="rgb(16, 185, 129)" />
                                    <stop offset="100%" stopColor="rgb(168, 85, 247)" />
                                </linearGradient>
                            </defs>
                            <Activity size={24} stroke="url(#iconGradient)" />
                        </svg>
                      </div>
                      <p>Upload both files</p>
                      <p className="text-xs">to enable crossfade</p>
                    </div>
                  </div>
                )}
              </div>
              
              {/* Audio B Upload - 2 columns */}
              <div className="lg:col-span-2">
                <FileUpload
                  label="Audio B"
                  color="purple"
                  file={trackB}
                  onFileSelect={setTrackBWithRecent}
                />
              </div>
            </div>
          </div>
        </section>

        {/* Only show analysis sections when audio is uploaded */}
        {hasAnyAudio && (
          <>
            {/* Waveform Players */}
            <section className="space-y-6">
              <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
                {trackA && (
                  <WaveformPlayer
                    ref={waveformPlayerARef}
                    file={trackA}
                    color="green"
                    label="Audio A"
                    onPlayStateChange={setIsTrackAPlaying}
                    onAudioLevels={setTrackAAudioLevels}
                    crossfadeVolume={volumeA}
                  />
                )}
                {trackB && (
                  <WaveformPlayer
                    ref={waveformPlayerBRef}
                    file={trackB}
                    color="purple"
                    label="Audio B"
                    onPlayStateChange={setIsTrackBPlaying}
                    onAudioLevels={setTrackBAudioLevels}
                    crossfadeVolume={volumeB}
                  />
                )}
              </div>
            </section>

            {/* Level Meters */}
            <section className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {trackA && (
                  <LevelMeter 
                    label="Audio A Levels" 
                    color="green" 
                    isActive={true} 
                    isPlaying={isTrackAPlaying}
                    audioLevels={trackAAudioLevels}
                    crossfadeVolume={volumeA}
                  />
                )}
                {trackB && (
                  <LevelMeter 
                    label="Audio B Levels" 
                    color="purple" 
                    isActive={true} 
                    isPlaying={isTrackBPlaying}
                    audioLevels={trackBAudioLevels}
                    crossfadeVolume={volumeB}
                  />
                )}
              </div>
            </section>
          </>
        )}
        </div>
      </div>
    </div>
  );
}

export default App;
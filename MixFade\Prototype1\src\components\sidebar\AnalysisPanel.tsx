import React from 'react';
import { BarChart3, TrendingUp, Activity, Zap } from 'lucide-react';

export function AnalysisPanel() {
  return (
    <div className="p-4 space-y-6">
      {/* Real-time Analysis */}
      <div className="space-y-2">
        <h3 className="text-xs font-semibold text-slate-400 uppercase tracking-wide">Real-time Analysis</h3>
        <div className="space-y-3">
          {/* Frequency Response */}
          <div className="p-3 bg-slate-800 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <BarChart3 size={16} className="text-emerald-500" />
              <span className="text-sm text-slate-300">Frequency Response</span>
            </div>
            <div className="text-xs text-slate-500">
              A vs B frequency comparison will be integrated here
            </div>
          </div>

          {/* Loudness Analysis */}
          <div className="p-3 bg-slate-800 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <TrendingUp size={16} className="text-blue-500" />
              <span className="text-sm text-slate-300">Loudness Analysis</span>
            </div>
            <div className="text-xs text-slate-500">
              LUFS, RMS, and dynamic range comparison
            </div>
          </div>

          {/* Stereo Analysis */}
          <div className="p-3 bg-slate-800 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <Activity size={16} className="text-purple-500" />
              <span className="text-sm text-slate-300">Stereo Analysis</span>
            </div>
            <div className="text-xs text-slate-500">
              Stereo width and phase correlation
            </div>
          </div>

          {/* Spectrogram */}
          <div className="p-3 bg-slate-800 rounded-lg">
            <div className="flex items-center space-x-2 mb-2">
              <Zap size={16} className="text-yellow-500" />
              <span className="text-sm text-slate-300">Spectrogram</span>
            </div>
            <div className="text-xs text-slate-500">
              Time-frequency analysis visualization
            </div>
          </div>
        </div>
      </div>

      {/* Analysis Settings */}
      <div className="space-y-2">
        <h3 className="text-xs font-semibold text-slate-400 uppercase tracking-wide">Analysis Settings</h3>
        <div className="space-y-3">
          {/* Analysis Resolution */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm text-slate-300">FFT Size</label>
              <span className="text-xs text-slate-500">2048</span>
            </div>
            <select className="w-full bg-slate-700 text-slate-300 text-sm rounded px-2 py-1">
              <option value="1024">1024</option>
              <option value="2048" selected>2048</option>
              <option value="4096">4096</option>
              <option value="8192">8192</option>
            </select>
          </div>

          {/* Update Rate */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <label className="text-sm text-slate-300">Update Rate</label>
              <span className="text-xs text-slate-500">30 FPS</span>
            </div>
            <input
              type="range"
              min="10"
              max="60"
              defaultValue="30"
              className="w-full h-2 bg-slate-700 rounded-lg appearance-none cursor-pointer slider"
            />
          </div>
        </div>
      </div>

      {/* Export Options */}
      <div className="space-y-2">
        <h3 className="text-xs font-semibold text-slate-400 uppercase tracking-wide">Export</h3>
        <div className="space-y-2">
          <button className="w-full flex items-center justify-center space-x-2 px-3 py-2 text-sm text-slate-300 bg-slate-800 hover:bg-slate-700 rounded-md transition-colors">
            <BarChart3 size={14} />
            <span>Export Analysis Report</span>
          </button>
        </div>
      </div>
    </div>
  );
}
